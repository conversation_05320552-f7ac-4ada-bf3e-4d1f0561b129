# 115网盘DPlayer播放器

这是从原始的 `lorax115优化大师` 脚本中提取出来的专门用于DPlayer播放功能的独立脚本。

## 功能特性

### 核心播放功能
- **DPlayer播放器集成**：使用DPlayer播放115网盘中的视频文件
- **多清晰度支持**：自动获取并支持多种清晰度播放（原画、4K、蓝光、超清、高清、标清）
- **M3U8播放列表**：支持HLS流媒体播放
- **播放记录同步**：支持云端播放记录，自动记录播放进度
- **字幕功能**：自动获取并显示115网盘视频的字幕文件，支持多字幕切换

### 播放控制功能
- **跳过片头片尾**：可设置自动跳过片头和片尾的秒数
- **画中画模式**：支持浏览器画中画功能
- **播放器跟随页面**：页面切换到后台时自动暂停，切换到前台时自动播放
- **自动窗口调整**：播放器窗口大小自动适应浏览器窗口

### 视频管理功能
- **右键菜单**：播放器右键菜单支持下载、删除、重命名、设置星标等操作
- **文件夹管理**：可以查看和删除视频所在文件夹
- **下载功能**：支持直接下载正在播放的视频文件
- **校验码显示**：下载时可选择显示文件SHA1校验码

## 使用方法

### 安装
1. 安装Tampermonkey或Greasemonkey等用户脚本管理器
2. 将 `115-dplayer-only.js` 脚本安装到脚本管理器中
3. 访问115网盘即可使用

### 字幕功能使用
1. **自动加载**：播放视频时，如果视频有字幕文件，会根据设置自动加载
2. **手动切换**：右键点击播放器，选择"字幕选择"可以切换不同的字幕
3. **关闭字幕**：在字幕选择菜单中选择"关闭字幕"
4. **配置设置**：在脚本设置中可以配置默认字幕语言和开关

### 配置
点击脚本管理器中的"DPlayer设置"菜单可以进行以下配置：

#### 字幕设置
- **启用字幕功能**：开启/关闭字幕功能
- **默认字幕语言**：设置默认字幕语言（关闭/中文/英文/自动选择）

#### 播放设置

#### 播放设置
- **默认播放清晰度**：最高/次高/最低
- **跳过片头秒数**：设置自动跳过片头的时间
- **跳过片尾秒数**：设置自动跳过片尾的时间
- **开启云端记忆播放**：是否启用播放进度云端同步
- **播放器跟随页面变化**：页面后台时是否自动暂停

#### 界面设置
- **隐藏第三方播放器悬浮按钮**：是否隐藏播放按钮
- **通知弹出位置**：设置通知消息的显示位置
- **下载后保存校验码**：下载文件时是否保存SHA1校验码

### 播放操作
1. **在115网盘文件列表中**：
   - 鼠标悬停在视频文件上会显示"Dp播放"按钮
   - 点击文件名会使用DPlayer播放
   - 双击文件（非文件名区域）会使用官方HTML5播放器

2. **在DPlayer播放界面中**：
   - 右键菜单提供丰富的操作选项
   - 支持多清晰度切换
   - 支持画中画模式
   - 支持截图功能

## 技术特点

### 依赖库
- jQuery 3.7.1：DOM操作和AJAX请求
- GM_config：配置管理界面
- HLS.js 1.5.1：HLS流媒体支持
- Toastr 2.1.4：通知消息显示
- DPlayer 1.26.0：视频播放器核心

### 兼容性
- 支持Chrome浏览器
- 需要Tampermonkey或类似的用户脚本管理器
- 仅在115.com域名下工作

### 作用范围
脚本的作用范围与原始脚本保持一致：
- 包含：`http*://*.115.com/*`
- 排除：桥接页面、静态资源页面等

## 注意事项

1. **登录要求**：使用前需要先登录115网盘账号
2. **网络要求**：需要稳定的网络连接来加载依赖库和视频流
3. **浏览器支持**：某些功能（如画中画）需要现代浏览器支持
4. **转码等待**：部分视频可能需要等待115服务器转码完成

## 与原脚本的区别

这个独立版本专注于DPlayer播放功能，移除了：
- 离线下载功能
- 批量操作功能
- 文件管理功能
- 磁力链接处理
- 其他非播放相关的功能

保留了所有与DPlayer播放相关的核心功能，使脚本更加轻量和专注。

## 版本信息
- 版本：v1.0
- 基于：lorax115优化大师 v7.0
- 作者：zxf10608（原作者）
- 提取整理：根据用户需求提取

## 许可证
GPL License（与原脚本保持一致）
