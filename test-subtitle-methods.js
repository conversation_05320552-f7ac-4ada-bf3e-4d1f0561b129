// 115网盘字幕修复测试脚本
// 用于测试不同的字幕加载方法

console.log('=== 115网盘字幕修复测试开始 ===');

// 测试用的字幕URL（从用户错误信息中提取）
const TEST_SUBTITLE_URL = 'https://aps.115.com/transcode/1751781935/31267f5../CE9B0userscript.html?name=c-c789f98bce33:16405706C2584A658F0ECE2DB4E56217B76962E?type=.srt';

// 测试方法1: 简单fetch（不添加自定义头部）
async function testSimpleFetch() {
    console.log('🧪 测试方法1: 简单fetch');
    
    try {
        console.log('📡 发送请求:', TEST_SUBTITLE_URL);
        
        const response = await fetch(TEST_SUBTITLE_URL, {
            method: 'GET',
            mode: 'cors'  // 明确指定CORS模式
        });
        
        console.log('📊 响应状态:', response.status, response.statusText);
        console.log('📋 响应头:', [...response.headers.entries()]);
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const text = await response.text();
        console.log('✅ 简单fetch成功！字幕长度:', text.length);
        console.log('📄 内容预览:', text.substring(0, 200));
        
        return { success: true, method: 'simple-fetch', data: text };
    } catch (error) {
        console.log('❌ 简单fetch失败:', error.message);
        return { success: false, method: 'simple-fetch', error: error.message };
    }
}

// 测试方法2: CORS代理
async function testCorsProxy() {
    console.log('🧪 测试方法2: CORS代理');
    
    const proxyUrls = [
        `https://api.allorigins.win/get?url=${encodeURIComponent(TEST_SUBTITLE_URL)}`,
        `https://corsproxy.io/?${encodeURIComponent(TEST_SUBTITLE_URL)}`
    ];
    
    for (let i = 0; i < proxyUrls.length; i++) {
        try {
            const proxyUrl = proxyUrls[i];
            console.log(`📡 尝试代理 ${i + 1}:`, proxyUrl);
            
            const response = await fetch(proxyUrl);
            console.log('📊 代理响应状态:', response.status, response.statusText);
            
            if (!response.ok) {
                throw new Error(`代理响应失败: ${response.status}`);
            }
            
            const data = await response.json();
            const subtitleText = data.contents || data.data || data;
            
            if (typeof subtitleText !== 'string') {
                throw new Error('代理返回的数据格式不正确');
            }
            
            console.log(`✅ 代理方法 ${i + 1} 成功！字幕长度:`, subtitleText.length);
            console.log('📄 内容预览:', subtitleText.substring(0, 200));
            
            return { success: true, method: `cors-proxy-${i + 1}`, data: subtitleText };
        } catch (error) {
            console.log(`❌ 代理 ${i + 1} 失败:`, error.message);
        }
    }
    
    return { success: false, method: 'cors-proxy', error: '所有代理都失败了' };
}

// 测试方法3: 检查网络连通性
async function testConnectivity() {
    console.log('🧪 测试方法3: 网络连通性检查');
    
    try {
        // 测试115网盘主站
        console.log('🌐 检查115网盘主站连通性...');
        await fetch('https://115.com', { 
            method: 'HEAD',
            mode: 'no-cors'
        });
        console.log('✅ 115网盘主站连通正常');
        
        // 测试字幕服务器
        console.log('🌐 检查字幕服务器连通性...');
        await fetch('https://aps.115.com', { 
            method: 'HEAD',
            mode: 'no-cors'
        });
        console.log('✅ 字幕服务器连通正常');
        
        return { success: true, method: 'connectivity' };
    } catch (error) {
        console.log('❌ 网络连通性检查失败:', error.message);
        return { success: false, method: 'connectivity', error: error.message };
    }
}

// 运行所有测试
async function runAllTests() {
    console.log('🚀 开始运行所有测试...');
    
    const results = [];
    
    // 测试1: 简单fetch
    results.push(await testSimpleFetch());
    
    // 等待一秒
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 测试2: CORS代理
    results.push(await testCorsProxy());
    
    // 等待一秒
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 测试3: 网络连通性
    results.push(await testConnectivity());
    
    // 输出测试结果总结
    console.log('\n📊 测试结果总结:');
    console.log('==================');
    
    results.forEach((result, index) => {
        const status = result.success ? '✅ 成功' : '❌ 失败';
        console.log(`测试 ${index + 1} (${result.method}): ${status}`);
        if (!result.success && result.error) {
            console.log(`   错误: ${result.error}`);
        }
    });
    
    // 提供建议
    console.log('\n💡 建议:');
    const successfulMethods = results.filter(r => r.success);
    
    if (successfulMethods.length === 0) {
        console.log('❌ 所有方法都失败了，可能需要：');
        console.log('   1. 检查网络连接');
        console.log('   2. 使用用户脚本管理器（如Tampermonkey）');
        console.log('   3. 尝试浏览器扩展方案');
    } else {
        console.log('✅ 以下方法可用：');
        successfulMethods.forEach(method => {
            console.log(`   - ${method.method}`);
        });
    }
    
    console.log('\n=== 测试完成 ===');
    return results;
}

// 如果在浏览器环境中，自动运行测试
if (typeof window !== 'undefined') {
    // 等待页面加载完成
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', runAllTests);
    } else {
        runAllTests();
    }
}

// 导出函数供外部调用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        testSimpleFetch,
        testCorsProxy,
        testConnectivity,
        runAllTests
    };
}
