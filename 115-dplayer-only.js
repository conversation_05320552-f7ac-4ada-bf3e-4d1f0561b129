// ==UserScript==
// @name         115网盘DPlayer播放器
// <AUTHOR>
// @version      1.0
// @icon         https://cdn.jsdelivr.net/gh/zxf10608/JavaScript/icon/115logo.ico
// @namespace    https://greasyfork.org/zh-CN/scripts/dplayer-115
// @description  专门提取115网盘DPlayer播放功能的独立脚本
// @require      https://cdn.jsdelivr.net/npm/jquery@3.7.1/dist/jquery.min.js
// @require      https://greasyfork.org/scripts/398240-gm-config-zh-cn/code/GM_config_zh-CN.js
// @require      https://cdn.jsdelivr.net/npm/hls.js@1.5.1/dist/hls.min.js
// @require      https://cdn.jsdelivr.net/npm/toastr@2.1.4/toastr.min.js
// @resource     toastrCss   https://cdn.jsdelivr.net/npm/toastr@2.1.4/build/toastr.min.css
// @require      https://cdn.jsdelivr.net/npm/dplayer@1.26.0/dist/DPlayer.min.js
// @require      https://cdn.jsdelivr.net/npm/dplayer-srt@2.0.1/lib/DPlayerSubtitles.min.js
// @resource     dplayerCss  https://cdn.jsdelivr.net/npm/dplayer/dist/DPlayer.min.css
// @include      *
// @include      http*://*.115.com/*
// @exclude      http*://*.115.com/bridge*
// @exclude      http*://*.115.com/*/static*
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_addStyle
// @grant        GM_openInTab
// @grant        GM_setClipboard
// @grant        GM_getResourceText
// @grant        GM_xmlhttpRequest
// @grant        GM_registerMenuCommand
// @connect      115.com
// @connect      *
// @grant        unsafeWindow
// @grant        window.open
// @grant        window.close
// @run-at       document-start
// @compatible   chrome
// @license      GPL License
// ==/UserScript==

(function() {
    'use strict';
    
    var newVersion = 'v1.0';
    
    if (typeof GM_config == 'undefined') {
        alert('115 DPlayer播放器：\n网络异常，相关库文件加载失败，脚本无法使用，请刷新网页重新加载！');
        return;
    } else {
        console.log('115 DPlayer播放器：相关库文件加载成功！');
    }
    
    // 配置管理
    function config() {
        var windowCss = '#Cfg .nav-tabs {margin: 20 2} #Cfg .config_var textarea{width: 310px; height: 50px;} #Cfg .inline {padding-bottom:0px;} #Cfg .config_header a:hover {color:#1e90ff;} #Cfg .config_var {margin-left: 10%;margin-right: 10%;} #Cfg input[type="checkbox"] {margin: 3px 3px 3px 0px;} #Cfg input[type="text"] {width: 53px;} #Cfg {background-color: lightblue;} #Cfg .reset_holder {float: left; position: relative; bottom: -1em;} #Cfg .saveclose_buttons {margin: .7em;} #Cfg .section_desc {font-size: 10pt;}';
        
        GM_registerMenuCommand('DPlayer设置', opencfg);
        function opencfg() {
            GM_config.open();
        }
        
        GM_config.init({
            id: 'Cfg',
            title: GM_config.create('a', {
                href: 'https://greasyfork.org/zh-CN/scripts/408466',
                target: '_blank',
                className: 'setTitle',
                textContent: '115网盘DPlayer播放器',
                title: '提取自lorax115优化大师 版本：' + newVersion
            }),
            css: windowCss,
            frameStyle: {
                height: '400px',
                width: '445px',
                zIndex: '2147483648',
            },
            fields: {
                play_Quality: {
                    section: ['播放设置', 'DPlayer播放器相关设置'],
                    label: '默认播放清晰度',
                    labelPos: 'left',
                    type: 'select',
                    'options': ['最高', '次高', '最低'],
                    default: '最高',
                },
                skip_titles: {
                    label: '跳过片头秒数',
                    type: 'unsigned int',
                    default: '0',
                },
                skip_credits: {
                    label: '跳过片尾秒数',
                    type: 'unsigned int',
                    default: '0',
                },
                online_List: {
                    label: '开启云端记忆播放',
                    labelPos: 'right',
                    type: 'checkbox',
                    default: true,
                },
                Tab_ing: {
                    label: '播放器跟随页面变化',
                    labelPos: 'right',
                    type: 'checkbox',
                    default: false,
                },
                show_sha: {
                    label: '下载后保存校验码',
                    labelPos: 'right',
                    type: 'checkbox',
                    default: false,
                },
                toastr: {
                    label: '通知弹出位置',
                    labelPos: 'left',
                    type: 'select',
                    'options': ['左上', '右上', '中上', '全铺'],
                    default: '右上',
                },
                http_ua: {
                    label: '数据请求UA标识（非必要勿改）',
                    type: 'textarea',
                    default: ''
                },
                enable_subtitle: {
                    label: '启用字幕功能',
                    labelPos: 'right',
                    type: 'checkbox',
                    default: true,
                },
                default_subtitle: {
                    label: '默认字幕语言',
                    labelPos: 'left',
                    type: 'select',
                    'options': ['关闭', '中文', '英文', '自动选择'],
                    default: '自动选择',
                },
                hide_play: {
                    label: '隐藏第三方播放器悬浮按钮',
                    labelPos: 'right',
                    type: 'checkbox',
                    default: false,
                }
            },
            events: {
                save: function() {
                    GM_config.close();
                    location.reload();
                }
            },
        });
    }
    config();
    
    var G = GM_config;
    var localHref = window.location.href;
    var UA = G.get('http_ua') != '' ? G.get('http_ua') : navigator.userAgent;
    
    console.log('115 DPlayer脚本UA：' + UA);
    
    // 通知系统初始化
    function notice() {
        GM_addStyle(GM_getResourceText('toastrCss'));
        if (G.get('toastr') == '全铺' || localHref.indexOf('https://captchaapi.115.com') != -1) {
            GM_addStyle('.toast{font-size:15px!important;} .toast-title{font-size:16px!important;text-align:center}');
        } else {
            GM_addStyle('.toast{font-size:15px!important;width:360px!important;} .toast-title{font-size:16px!important;text-align:center}');
        }
        var place = { '左上': 'toast-top-left', '右上': 'toast-top-right', '中上': 'toast-top-center' }[G.get('toastr')] || 'toast-top-full-width';
        toastr.options = {
            "closeButton": true,
            "debug": false,
            "progressBar": true,
            "timeOut": 8000,
            "extendedTimeOut": 8000,
            "positionClass": place,
            "allowHtml": true,
            "newestOnTop": false,
        };
    }
    notice();
    
    // AJAX请求函数
    function AjaxCall(href, callback) {
        GM_xmlhttpRequest({
            method: "GET",
            url: href,
            headers: {
                "User-Agent": UA,
                Origin: "https://115.com",
            },
            onload: function(data, status) {
                if (data.readyState == 4 && data.status == 200) {
                    var htmlTxt = data.responseText;
                    callback(null, htmlTxt);
                }
            },
            onerror: function(error) {
                callback(error);
            },
            ontimeout: function(error) {
                callback(error);
            },
        });
    }
    
    // 下载函数
    function download(key, num) {
        return new Promise(function(resolve, reject) {
            var batch = typeof num != 'undefined' ? true : false;
            var href = `https://webapi.115.com/files/download?pickcode=${key.pc}&_=${new Date().getTime()}`;
            AjaxCall(href, function(error, htmlTxt) {
                if (error) {
                    console.log('网络错误，获取文件地址失败！');
                    resolve([false, error]);
                    return;
                }
                var json = JSON.parse(htmlTxt);
                
                if (json.state) {
                    var link = json.file_url;
                    batch ? resolve([link, key.n, num, key.sha]) : resolve([link]);
                } else {
                    originLink(key.pc, key.fid, '115origin').then(function(origin) {
                        if (origin[0]) {
                            var link = origin[0];
                            batch ? resolve([link, key.n, num, key.sha]) : resolve([link]);
                        } else {
                            batch ? resolve([false, key.n, num, origin[1]]) : resolve([false, origin[1]]);
                        }
                    });
                }
            });
        });
    }
    
    // 获取原始链接
    function originLink(pid, fid, type, name, i) {
        return new Promise(function(resolve, reject) {
            if (type != '115origin' && (G.get('play_Quality') != '原码' || ((type == '115play') || type == 'Dp'))) {
                resolve([false, 'ignore']);
                return;
            }
            var href = 'https://proapi.115.com/app/chrome/down?method=get_file_url&pickcode=' + pid;
            AjaxCall(href, function(error, htmlTxt) {
                if (error) {
                    console.log('网络错误，获取文件地址失败！');
                    resolve([false, error]);
                    return;
                }
                var json = JSON.parse(htmlTxt);
                if (json.state) {
                    var link = json.data[fid].url.url;
                    name ? resolve([link, name, i]) : resolve([link]);
                } else {
                    if (name) {
                        resolve([false, name, i, json.msg]);
                    } else {
                        resolve([false, json.msg]);
                    }
                }
            });
        });
    }
    
    // 获取播放历史记录
    function getHistory(pid) {
        return new Promise(function(resolve, reject) {
            var href = 'https://webapi.115.com/files/history?pick_code=' + pid + '&fetch=one&category=1';
            AjaxCall(href, function(error, htmlTxt) {
                var time = 0;
                if (error) {
                    console.log('网络错误，获取播放记录失败！');
                    resolve(time);
                }
                var json = JSON.parse(htmlTxt);
                if (json.state) {
                    if (!json.data.watch_end) {
                        time = json.data.time;
                    }
                }
                resolve(time);
            });
        });
    }

    // 获取字幕列表
    function getSubtitles(pid) {
        return new Promise(function(resolve, reject) {
            if (!G.get('enable_subtitle')) {
                resolve([]);
                return;
            }

            var href = 'https://webapi.115.com/movies/subtitle?pickcode=' + pid;
            AjaxCall(href, function(error, htmlTxt) {
                if (error) {
                    console.log('获取字幕失败:', error);
                    resolve([]);
                    return;
                }

                try {
                    var json = JSON.parse(htmlTxt);
                    if (json.state && json.data && json.data.list) {
                        var subtitles = [];
                        json.data.list.forEach(function(sub, index) {
                            var url = (sub.url && sub.url.startsWith('http://'))
                                ? sub.url.replace(/^http:\/\//, 'https://')
                                : sub.url;
                            // Determine subtitle type: SRT if URL indicates .srt, otherwise WebVTT
                            var subtype = 'webvtt';
                            if (url.toLowerCase().includes('type=.srt') || /\.srt(\?.*)?$/i.test(url)) {
                                subtype = 'srt';
                            }
                            subtitles.push({
                                name: sub.name || ('字幕' + (index + 1)),
                                url: url,
                                type: subtype,
                                lang: sub.lang || 'zh-CN'
                            });
                        });
                        console.log('获取到字幕列表:', subtitles);
                        resolve(subtitles);
                    } else {
                        resolve([]);
                    }
                } catch (e) {
                    console.log('解析字幕数据失败:', e);
                    resolve([]);
                }
            });
        });
    }

    // 获取M3U8播放列表
    function getM3u8(video, type) {
        return new Promise(function(resolve, reject) {
            var herfLink = 'https://115.com/api/video/m3u8/' + video.pid + '.m3u8';
            AjaxCall(herfLink, function(error, htmlTxt) {
                if (typeof htmlTxt == 'undefined') {
                    transcoding(video, type);
                    resolve([false, video]);
                    return;
                }
                var dataList = htmlTxt.split('\n');
                var m3u8 = [];
                var temp = '"YH"|原画|"BD"|4K|"UD"|蓝光|"HD"|超清|"SD"|高清|"3G"|标清';
                var txt = temp.split('|');
                for (var i = 0; i < 6; i++) {
                    dataList.forEach(function(e, j, arr) {
                        if (e.indexOf(txt[i * 2]) != -1) {
                            m3u8.push({ name: txt[i * 2 + 1], url: arr[j + 1].replace(/\r/g, ''), type: 'hls' });
                        }
                    });
                }

                if (m3u8.length == 1 || G.get('play_Quality') == '最高' || G.get('play_Quality') == '原码') {
                    var num = 0;
                } else if (m3u8.length > 1 && G.get('play_Quality') == '次高') {
                    var num = 1;
                } else {
                    var num = m3u8.length - 1;
                }
                video['quality'] = num;
                resolve([m3u8, video, num]);
            });
        });
    }

    // 转码处理
    function transcoding(video, type) {
        var href = 'https://transcode.115.com/api/1.0/web/1.0/trans_code/check_transcode_job?sha1=' + video.sha + '&priority=100';
        console.log('转码进度地址:' + href);
        AjaxCall(href, function(error, htmlTxt) {
            var json = JSON.parse(htmlTxt);
            if (json.status == 1 || json.status == 3) {
                var num = json.count;
                var time = tranTime(json.time).replace(/分.*/, '分');
                var txt = `等待转码排名：第${num}名，耗时：约${time}，请稍后再试。`;
            } else if (json.status == 127) {
                var txt = '未获取到转码进度，请稍后再试或选择原码播放。';
                console.log('查询转码进度失败');
            }
            var videoTxt = JSON.stringify(video);
            var h1 = `<br><a target="_blank" class="transcode_show" data=${videoTxt} href="javascript:void(0);" style="cursor:pointer;color:blue;" title="打开转码进度详情页">转码详情</a>`;
            var h2 = '';
            var h3 = '';
            if (type == 2) {
                return;
            } else if (type == 1) {
                var title = '加速转码成功！';
            } else if (type) {
                var title = '加速转码失败！';
                var txt = type;
            } else {
                var title = '播放失败，视频未转码！';
                h2 = `&nbsp;&nbsp;<a target="_blank" class="transcode_fast" data=${videoTxt} href="javascript:void(0);" style="cursor:pointer;color:blue;" title="加速转码进度">加速转码</a>`;
            }

            toastr.warning(txt + h1 + h2 + h3, title, { timeOut: 10000 });
        });
    }

    // 时间转换函数
    function tranTime(num) {
        var showTime = '';
        if (num > 3600) { showTime += ' ' + parseInt(num / 3600) + ' 小时'; num = num % 3600; }
        if (num > 60) { showTime += ' ' + parseInt(num / 60) + ' 分'; num = num % 60; }
        return showTime += ' ' + parseInt(num) + ' 秒';
    }

    // 画中画功能
    function enterPiP(videoEl) {
        if (document.pictureInPictureEnabled && !videoEl.disablePictureInPicture) {
            if (!document.pictureInPictureElement) {
                videoEl.requestPictureInPicture();
            } else {
                document.exitPictureInPicture();
            }
        } else {
            alert('浏览器不支持或已关闭画中画功能！');
        }
    }

    // 文件大小转换
    function change(number) {
        var size = "";
        if (number < 1024 * 1024 * 1024) {
            size = (number / (1024 * 1024)).toFixed(2) + "MB";
        } else {
            size = (number / (1024 * 1024 * 1024)).toFixed(2) + "GB";
        }
        var sizeStr = size + "";
        var index = sizeStr.indexOf(".");
        var dou = sizeStr.substr(index + 1, 2)
        if (dou == "00") {
            return sizeStr.substring(0, index) + sizeStr.substr(index + 3, 2)
        }
        return size;
    }

    // 文件名处理
    function name2(txt) {
        var newName = txt.replace(/\.(?!\w{2,4}$)/g, '_')
            .replace(/\s/g, '&nbsp;');
        return newName;
    }

    // 自动调整播放器窗口大小
    function autobox() {
        if (document.compatMode == 'BackCompat') {
            var cW = document.body.clientWidth;
            var cH = document.body.clientHeight;
            var sW = document.body.scrollWidth;
            var sH = document.body.scrollHeight;
        } else {
            var cW = document.documentElement.clientWidth;
            var cH = document.documentElement.clientHeight;
            var sW = document.documentElement.scrollWidth;
            var sH = document.documentElement.scrollHeight;
        }
        var iW = window.innerWidth;
        var iH = window.innerHeight;
        var eW = $('#Dplayer')[0].offsetWidth;
        var eH = $('#Dplayer')[0].offsetHeight;

        if (sW > (iW || cW)) {
            cW = iW || cW;
        }
        if (sH > (iH || cH)) {
            cH = iH || cH;
        }

        $('#Dplayer').css({ 'width': cW + 'px', 'height': cH + 'px' });
    }

    // 播放数据处理
    function palyData(video, type) {
        if (type == 'dblclick') {
            var link = 'https://115.com/?ct=play&ac=location&pickcode=' + video.pid + '&hls=1';
            GM_openInTab(link, false);
            return;
        }

        originLink(video.pid, video.fid, type, video.name).then(function(origin) {
            if (origin[0]) {
                switchPlayer(origin, type);
                return;
            } else if (origin[1] != 'ignore') {
                toastr.warning('获取视频原码地址失败，将播放转码最高清晰度。', '播放原码失败!', { timeOut: 6000 });
            }

            getM3u8(video).then(function(data) {
                if (!data[0]) {
                    toastr.warning('未获取播放地址。', '播放失败！');
                    return;
                }
                GM_setValue('videoInfo', data[1]);
                GM_setValue('m3u8List', data[0]);
                switchPlayer(origin, type, data[0][data[2]], data[1].name);
            });
        }, function(error) {
            toastr.error('服务器繁忙，请稍后再试。', '操作异常!');
            console.log(error);
        });
    }

    // 播放器切换
    function switchPlayer(origin, type, m3u8, n) {
        if (origin[0]) {
            var link = origin[0];
            var name = origin[1];
            var definition = '原码';
        } else if (m3u8) {
            var link = m3u8.url;
            var name = n;
            var definition = m3u8.name;
        } else {
            toastr.error('未知错误，请稍后再试。', '播放失败!');
            return;
        }
        var txt = '';
        if (type == 'Dp') {
            var Dp = true;
            if (origin[0]) var txt = 'Dplayer不支持原码播放，';
        } else {
            var Dp = false;
        }

        if (m3u8 && Dp) {
            if (link.match(/https?:\/\/videotsgo\.115\.com/) != null) {
                GM_openInTab('https://videotsgo.115.com', false);
                return;
            }
            GM_openInTab('https://115.com/web/lixian/', false);
        } else {
            GM_setClipboard(link);
            toastr.success(txt + '请使用其他播放器打开该地址。', `<span style="color:purple;">${definition}</span> 地址复制成功！`, { timeOut: 8000 });
        }
    }

    // 离线操作对象
    var offline = function() {
        return {
            getData: function(url, key) {
                return new Promise(function(resolve, reject) {
                    GM_xmlhttpRequest({
                        method: 'POST',
                        url: url,
                        data: key,
                        headers: {
                            "User-Agent": UA,
                            "Content-Type": "application/x-www-form-urlencoded",
                            Origin: "https://115.com",
                        },
                        onload: function(result) {
                            if (result.responseText.indexOf('html') != -1) {
                                toastr.error('请先登录115网盘账号！', '操作失败。');
                                return;
                            }
                            var json = JSON.parse(result.responseText);
                            resolve(json);
                        },
                        onerror: function(error) {
                            reject(error);
                        },
                        ontimeout: function(error) {
                            reject(error);
                        },
                    });
                });
            },

            del: function(fid) {
                var key = { 'fid[0]': fid };
                this.getData('https://webapi.115.com/rb/delete', $.param(key)).then(function(json) {
                    console.log('删除文件结果:');
                    console.log(json);
                    if (json.state) {
                        toastr.success('文件删除成功！');
                        setTimeout(function() {
                            window.close();
                        }, 2000);
                    } else {
                        toastr.warning(json.error, '文件删除失败!');
                    }
                });
            },

            setStar: function(fid, star) {
                var key = { 'file_id': fid, 'star': star };
                this.getData('https://webapi.115.com/files/star', $.param(key)).then(function(json) {
                    console.log('设置星标结果:');
                    console.log(json);
                    json.state ? toastr.success('星标设置成功！') : toastr.warning(json.error, '星标设置失败!');
                });
            },

            newName: function(fid, name) {
                var new_name = prompt('请输入新的文件名：', name.replace(/&nbsp;/g, ' '));
                if (new_name == null) {
                    console.log('已点击取消');
                    return;
                } else if (new_name == '') {
                    toastr.warning('文件名不能为空！');
                    return;
                }
                var key = { 'fid': fid, 'file_name': new_name };
                this.getData('https://webapi.115.com/files/batch_rename', $.param(key)).then(function(json) {
                    console.log('重命名结果:');
                    console.log(json);
                    json.state ? toastr.success('文件重命名成功！') : toastr.warning(json.error, '文件重命名失败!');
                });
            }
        };
    }();

    // DPlayer播放器主要功能
    if (localHref.match(/^https?:\/\/(115\.com\/web\/lixian)|(videotsgo\.115\.com)\/$/) != null) {
        var m3u8 = GM_getValue('m3u8List');
        var video = GM_getValue('videoInfo');
        var titleTxt = video.name;
        var pickID = video.pid;
        var folderID = video.cid;
        var videoID = video.fid;
        var size = video.size;
        var sha = video.sha;
        var z = video.quality;
        var skipTime = G.get('skip_titles');
        var skipTime2 = G.get('skip_credits');
        GM_setValue('stop', true);

        $('pre').remove();
        $('body').html('');
        $('head').html(`<meta http-equiv="Content-Type" content="text/html; charset=GBK"><title>${titleTxt} ${size}</title>`);
        GM_addStyle(`html,body,div{margin:0;padding:0;border:0;outline:0;background:transparent}`);
        GM_addStyle(GM_getResourceText('dplayerCss'));
        $('body').append('<div id="Dplayer"></div>');

        function playVideo(m3u8) {
            // 打印m3u8播放列表信息到控制台
            console.log('=== DPlayer M3U8 播放列表 ===');
            console.log('视频名称:', titleTxt);
            console.log('m3u8列表:', m3u8);
            if (m3u8 && m3u8.length > 0) {
                m3u8.forEach((item, index) => {
                    console.log(`清晰度 ${index + 1}: ${item.name} - ${item.url}`);
                });
            }
            console.log('=============================');

            // 获取字幕并初始化播放器
            getSubtitles(pickID).then(function(subtitles) {
                initDPlayer(m3u8, subtitles);
            });
        }

        // 显示字幕选择菜单
        function showSubtitleMenu(dp, subtitles) {
            var menuHtml = '<div style="position:fixed;top:50%;left:50%;transform:translate(-50%,-50%);background:#000;color:#fff;padding:20px;border-radius:8px;z-index:9999;max-width:300px;">';
            menuHtml += '<h3 style="margin:0 0 15px 0;text-align:center;">选择字幕</h3>';
            menuHtml += '<div style="margin-bottom:10px;"><button onclick="switchSubtitle(null)" style="width:100%;padding:8px;background:#333;color:#fff;border:1px solid #555;border-radius:4px;cursor:pointer;">关闭字幕</button></div>';

            subtitles.forEach(function(sub, index) {
                menuHtml += '<div style="margin-bottom:10px;"><button onclick="switchSubtitle(\'' + sub.url + '\')" style="width:100%;padding:8px;background:#333;color:#fff;border:1px solid #555;border-radius:4px;cursor:pointer;">' + sub.name + '</button></div>';
            });

            menuHtml += '<div style="text-align:center;margin-top:15px;"><button onclick="closeSubtitleMenu()" style="padding:8px 20px;background:#666;color:#fff;border:none;border-radius:4px;cursor:pointer;">取消</button></div>';
            menuHtml += '</div>';
            menuHtml += '<div id="subtitle-overlay" onclick="closeSubtitleMenu()" style="position:fixed;top:0;left:0;width:100%;height:100%;background:rgba(0,0,0,0.5);z-index:9998;"></div>';

            $('body').append(menuHtml);

            // 添加全局函数
            unsafeWindow.switchSubtitle = function(url) {
                console.log('切换字幕:', url);
                console.log('DPlayer video元素:', dp.video);

                try {

                if (url) {
                    console.log('加载字幕:', url);



                    // 使用多种方法尝试加载字幕
                    console.log('开始加载字幕:', url);
                    dp.notice('正在加载字幕...', 1000);

                    // 方法1: 使用简单的fetch（不添加自定义头部）
                    loadSubtitleSimpleFetch(dp, url)
                        .catch(function(error) {
                            console.log('简单fetch失败，尝试代理方法:', error.message);
                            // 方法2: 使用CORS代理
                            return loadSubtitleWithProxy(dp, url);
                        })
                        .catch(function(error) {
                            console.log('代理方法失败，尝试iframe方法:', error.message);
                            // 方法3: 使用iframe方法
                            return loadSubtitleWithIframe(dp, url);
                        })
                        .catch(function(error) {
                            console.log('iframe方法失败，尝试直接加载:', error.message);
                            // 方法4: 直接使用track元素（最后尝试）
                            return loadSubtitleDirect(dp, url);
                        })
                        .catch(function(error) {
                            console.error('所有方法都失败了:', error);
                            dp.notice('字幕加载失败: ' + error.message, 3000);
                        });

                    dp.notice('正在加载字幕...', 1000);
                    // Log each track’s mode and cue count after switching
                    Array.from(dp.video.textTracks).forEach(function(track, idx) {
                        console.log(`Track ${idx}: label="${track.label}", mode="${track.mode}", cues=${track.cues ? track.cues.length : 0}, activeCues=${track.activeCues ? track.activeCues.length : 0}`);
                    });
                    dp.notice('已切换字幕', 2000);
                } else {
                    console.log('Hiding subtitles');
                    subHandler.hide();
                    // Log each track’s mode and cue count after hiding
                    Array.from(dp.video.textTracks).forEach(function(track, idx) {
                        console.log(`Track ${idx}: label="${track.label}", mode="${track.mode}", cues=${track.cues ? track.cues.length : 0}`);
                    });
                    dp.notice('已关闭字幕', 2000);
                }
                } catch (error) {
                    console.error('字幕操作失败:', error);
                    dp.notice('字幕操作失败: ' + error.message, 3000);
                }

                closeSubtitleMenu();
            };

            unsafeWindow.closeSubtitleMenu = function() {
                $('#subtitle-overlay').parent().remove();
            };
        }

        // 字幕加载方法1: 简单fetch（不添加自定义头部）
        function loadSubtitleSimpleFetch(dp, url) {
            return new Promise(function(resolve, reject) {
                console.log('尝试简单fetch方法...');

                fetch(url, {
                    method: 'GET',
                    mode: 'cors'  // 明确指定CORS模式
                }).then(function(response) {
                    if (!response.ok) {
                        throw new Error('HTTP ' + response.status + ': ' + response.statusText);
                    }
                    return response.text();
                }).then(function(subtitleText) {
                    console.log('简单fetch成功，字幕长度:', subtitleText.length);
                    processSubtitleContent(dp, subtitleText);
                    resolve();
                }).catch(function(error) {
                    console.log('简单fetch失败:', error.message);
                    reject(error);
                });
            });
        }

        // 字幕加载方法2: 使用CORS代理
        function loadSubtitleWithProxy(dp, url) {
            return new Promise(function(resolve, reject) {
                console.log('尝试CORS代理方法...');

                // 使用公共CORS代理服务
                var proxyUrls = [
                    'https://api.allorigins.win/get?url=' + encodeURIComponent(url),
                    'https://corsproxy.io/?' + encodeURIComponent(url)
                ];

                var tryProxy = function(index) {
                    if (index >= proxyUrls.length) {
                        reject(new Error('所有代理都失败了'));
                        return;
                    }

                    var proxyUrl = proxyUrls[index];
                    console.log('尝试代理:', proxyUrl);

                    fetch(proxyUrl)
                        .then(function(response) {
                            if (!response.ok) {
                                throw new Error('代理响应失败: ' + response.status);
                            }
                            return response.json();
                        })
                        .then(function(data) {
                            var subtitleText = data.contents || data.data || data;
                            if (typeof subtitleText !== 'string') {
                                throw new Error('代理返回的数据格式不正确');
                            }
                            console.log('代理方法成功，字幕长度:', subtitleText.length);
                            processSubtitleContent(dp, subtitleText);
                            resolve();
                        })
                        .catch(function(error) {
                            console.log('代理' + index + '失败:', error.message);
                            tryProxy(index + 1);
                        });
                };

                tryProxy(0);
            });
        }

        // 字幕加载方法3: 使用iframe方法
        function loadSubtitleWithIframe(dp, url) {
            return new Promise(function(resolve, reject) {
                console.log('尝试iframe方法...');

                // 创建隐藏的iframe
                var iframe = document.createElement('iframe');
                iframe.style.display = 'none';
                iframe.src = url;

                var timeout = setTimeout(function() {
                    if (iframe.parentNode) {
                        document.body.removeChild(iframe);
                    }
                    reject(new Error('iframe方法超时'));
                }, 10000);

                iframe.onload = function() {
                    try {
                        var doc = iframe.contentDocument || iframe.contentWindow.document;
                        var subtitleText = doc.body.textContent || doc.body.innerText;

                        clearTimeout(timeout);
                        if (iframe.parentNode) {
                            document.body.removeChild(iframe);
                        }

                        if (subtitleText && subtitleText.length > 0) {
                            console.log('iframe方法成功，字幕长度:', subtitleText.length);
                            processSubtitleContent(dp, subtitleText);
                            resolve();
                        } else {
                            reject(new Error('iframe方法获取到空内容'));
                        }
                    } catch (error) {
                        clearTimeout(timeout);
                        if (iframe.parentNode) {
                            document.body.removeChild(iframe);
                        }
                        reject(new Error('iframe方法访问被阻止: ' + error.message));
                    }
                };

                iframe.onerror = function() {
                    clearTimeout(timeout);
                    if (iframe.parentNode) {
                        document.body.removeChild(iframe);
                    }
                    reject(new Error('iframe加载失败'));
                };

                document.body.appendChild(iframe);
            });
        }

        // 字幕加载方法4: 直接使用track元素
        function loadSubtitleDirect(dp, url) {
            return new Promise(function(resolve, reject) {
                console.log('尝试直接track方法...');

                // 移除现有字幕
                clearExistingSubtitles(dp);

                var track = document.createElement('track');
                track.kind = 'subtitles';
                track.src = url;
                track.srclang = 'zh-CN';
                track.label = '字幕';
                track.default = true;

                var timeout = setTimeout(function() {
                    reject(new Error('直接加载超时'));
                }, 10000);

                track.addEventListener('load', function() {
                    clearTimeout(timeout);
                    console.log('直接track方法成功');
                    track.mode = 'showing';
                    dp.notice('字幕已加载', 2000);
                    resolve();
                });

                track.addEventListener('error', function(e) {
                    clearTimeout(timeout);
                    reject(new Error('直接track加载失败: ' + e.message));
                });

                dp.video.appendChild(track);

                // 强制启用
                setTimeout(function() {
                    track.mode = 'showing';
                }, 100);
            });
        }

        // 处理字幕内容的通用函数
        function processSubtitleContent(dp, subtitleText) {
            console.log('处理字幕内容，长度:', subtitleText.length);
            console.log('字幕内容预览:', subtitleText.substring(0, 200));

            // 保存当前播放状态
            var wasPlaying = !dp.video.paused;
            var currentTime = dp.video.currentTime;

            // 清理现有字幕（但不影响播放）
            clearExistingSubtitles(dp);

            // 检查字幕格式并转换
            var vttContent = subtitleText;
            if (!vttContent.startsWith('WEBVTT')) {
                // 如果不是VTT格式，尝试转换SRT到VTT
                if (vttContent.includes('-->')) {
                    // 清理SRT格式并转换为VTT
                    var lines = vttContent.split('\n');
                    var vttLines = ['WEBVTT', ''];

                    for (var i = 0; i < lines.length; i++) {
                        var line = lines[i].trim();
                        if (!line) continue;

                        // 跳过纯数字行（SRT的序号）
                        if (/^\d+$/.test(line)) continue;

                        // 时间轴行
                        if (line.includes('-->')) {
                            // 转换SRT时间格式到VTT格式（逗号改为点）
                            line = line.replace(/,/g, '.');
                            vttLines.push(line);
                        } else {
                            // 字幕文本行
                            vttLines.push(line);
                        }
                    }

                    vttContent = vttLines.join('\n');
                    console.log('已转换SRT格式为VTT格式');
                } else {
                    console.warn('未知的字幕格式，尝试直接使用');
                    vttContent = 'WEBVTT\n\n' + vttContent;
                }
            }

            console.log('最终VTT内容预览:', vttContent.substring(0, 300));

            // 创建data URL
            var dataUrl = 'data:text/vtt;charset=utf-8,' + encodeURIComponent(vttContent);

            // 创建新的字幕轨道
            var track = document.createElement('track');
            track.kind = 'subtitles';
            track.src = dataUrl;
            track.srclang = 'zh-CN';
            track.label = '字幕1';
            track.default = true;

            // 添加到video元素
            dp.video.appendChild(track);

            // 等待轨道加载完成后启用
            track.addEventListener('load', function() {
                console.log('✅ 字幕轨道加载完成');
                track.mode = 'showing';

                // 检查字幕cues
                setTimeout(function() {
                    if (track.cues && track.cues.length > 0) {
                        console.log('📝 字幕cues数量:', track.cues.length);
                        console.log('🎯 字幕轨道模式:', track.mode);
                        dp.notice('字幕加载成功 (' + track.cues.length + ' 条)', 2000);

                        // 恢复播放状态
                        if (wasPlaying) {
                            dp.video.currentTime = currentTime;
                            dp.play();
                        }

                        // 重新设置字幕按钮处理器
                        setTimeout(function() {
                            setupSubtitleButtonHandler(dp);
                        }, 500);
                    } else {
                        console.warn('⚠️ 字幕轨道无内容');
                        dp.notice('字幕轨道无内容', 2000);
                    }
                }, 500);
            });

            track.addEventListener('error', function(e) {
                console.error('字幕轨道加载失败:', e);
                dp.notice('字幕轨道加载失败', 2000);

                // 恢复播放状态
                if (wasPlaying) {
                    dp.video.currentTime = currentTime;
                    dp.play();
                }
            });

            // 立即设置为显示模式
            setTimeout(function() {
                track.mode = 'showing';
                console.log('字幕已启用，模式:', track.mode);

                // 如果load事件没有触发，也要恢复播放状态
                setTimeout(function() {
                    if (wasPlaying && dp.video.paused) {
                        dp.video.currentTime = currentTime;
                        dp.play();
                    }
                }, 1000);
            }, 100);
        }

        // 清理现有字幕的函数（不影响播放状态）
        function clearExistingSubtitles(dp) {
            try {
                // 禁用所有现有字幕轨道
                if (dp.video.textTracks) {
                    for (var i = 0; i < dp.video.textTracks.length; i++) {
                        dp.video.textTracks[i].mode = 'disabled';
                    }
                }

                // 移除现有的track元素
                var existingTracks = dp.video.querySelectorAll('track');
                existingTracks.forEach(function(track) {
                    track.remove();
                });

                console.log('已清理现有字幕轨道');
            } catch (error) {
                console.error('清理字幕轨道时出错:', error);
            }
        }

        // 自动加载字幕函数
        function autoLoadSubtitle(dp, url) {
            console.log('自动加载字幕:', url);

            // 使用多种方法尝试加载字幕
            loadSubtitleSimpleFetch(dp, url)
                .then(function() {
                    console.log('✅ 自动加载字幕成功');
                    // 延迟更新按钮状态，确保字幕轨道已完全加载
                    setTimeout(function() {
                        updateSubtitleButtonState(dp);
                    }, 1000);
                })
                .catch(function(error) {
                    console.log('自动加载-简单fetch失败，尝试代理方法:', error.message);
                    return loadSubtitleWithProxy(dp, url);
                })
                .then(function(result) {
                    if (result) {
                        console.log('✅ 自动加载字幕成功（代理方法）');
                        setTimeout(function() {
                            updateSubtitleButtonState(dp);
                        }, 1000);
                    }
                })
                .catch(function(error) {
                    console.log('自动加载-代理方法失败，尝试iframe方法:', error.message);
                    return loadSubtitleWithIframe(dp, url);
                })
                .then(function(result) {
                    if (result) {
                        console.log('✅ 自动加载字幕成功（iframe方法）');
                        setTimeout(function() {
                            updateSubtitleButtonState(dp);
                        }, 1000);
                    }
                })
                .catch(function(error) {
                    console.log('自动加载-iframe方法失败，尝试直接加载:', error.message);
                    return loadSubtitleDirect(dp, url);
                })
                .then(function(result) {
                    if (result) {
                        console.log('✅ 自动加载字幕成功（直接方法）');
                        setTimeout(function() {
                            updateSubtitleButtonState(dp);
                        }, 1000);
                    }
                })
                .catch(function(error) {
                    console.error('自动加载字幕失败:', error);
                    dp.notice('自动加载字幕失败，可手动选择', 2000);
                });
        }

        // 调试DPlayer控件结构
        function debugDPlayerControls(dp) {
            console.log('🔍 调试DPlayer控件结构');

            // 查找控制栏
            var controller = dp.container.querySelector('.dplayer-controller');
            if (controller) {
                console.log('✅ 找到控制栏');

                // 列出所有控制按钮
                var buttons = controller.querySelectorAll('button, .dplayer-icon, [class*="dplayer"]');
                console.log('控制按钮列表:');
                buttons.forEach(function(btn, index) {
                    console.log(`按钮 ${index}:`, {
                        tagName: btn.tagName,
                        className: btn.className,
                        innerHTML: btn.innerHTML.substring(0, 50),
                        title: btn.title,
                        dataset: btn.dataset,
                        textContent: btn.textContent
                    });
                });
            } else {
                console.warn('⚠️ 未找到控制栏');
            }
        }

        // 添加字幕按钮样式
        function addSubtitleButtonStyles() {
            if (document.getElementById('subtitle-button-styles')) {
                return; // 样式已存在
            }

            var style = document.createElement('style');
            style.id = 'subtitle-button-styles';
            style.textContent = `
                /* 字幕按钮状态样式 */
                .dplayer-subtitle-btn-active,
                .dplayer-subtitle-button-active,
                .dplayer-icon-subtitle.active {
                    color: #007bff !important;
                    background-color: rgba(0, 123, 255, 0.1) !important;
                    border: 1px solid #007bff !important;
                }
                .dplayer-subtitle-btn,
                .dplayer-subtitle-button,
                .dplayer-icon-subtitle {
                    transition: all 0.3s ease !important;
                    border: 1px solid transparent !important;
                }
                .dplayer-subtitle-btn:hover,
                .dplayer-subtitle-button:hover,
                .dplayer-icon-subtitle:hover {
                    background-color: rgba(255, 255, 255, 0.1) !important;
                }
            `;
            document.head.appendChild(style);
            console.log('✅ 字幕按钮样式已添加');
        }

        // 设置字幕按钮处理器 - 修复点击功能
        function setupSubtitleButtonHandler(dp) {
            console.log('🎬 设置字幕按钮处理器');

            // 添加样式
            addSubtitleButtonStyles();

            // 调试控件结构
            debugDPlayerControls(dp);

            // 查找字幕按钮 - 尝试多种选择器
            var subtitleButton = dp.container.querySelector('.dplayer-subtitle-btn') ||
                                dp.container.querySelector('.dplayer-subtitle-button') ||
                                dp.container.querySelector('[data-balloon="字幕"]') ||
                                dp.container.querySelector('.dplayer-icon-subtitle');

            if (!subtitleButton) {
                console.warn('⚠️ 未找到字幕按钮，延迟重试');
                setTimeout(function() {
                    setupSubtitleButtonHandler(dp);
                }, 2000);
                return;
            }

            console.log('✅ 找到字幕按钮:', subtitleButton.className);

            // 首先更新按钮状态
            updateSubtitleButtonState(dp);

            // 移除DPlayer原生的点击事件监听器，添加我们自定义的
            var newButton = subtitleButton.cloneNode(true);
            subtitleButton.parentNode.replaceChild(newButton, subtitleButton);

            // 添加自定义的点击事件监听器
            newButton.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                console.log('🎯 字幕按钮被点击');
                toggleSubtitleDisplay(dp);

                // 延迟更新按钮状态
                setTimeout(function() {
                    updateSubtitleButtonState(dp);
                }, 100);
            });

            console.log('✅ 字幕按钮自定义事件监听器已设置');

            // 保存按钮引用
            dp._customSubtitleButton = newButton;
        }

        // 更新字幕按钮状态 - 使用DPlayer的事件系统
        function updateSubtitleButtonState(dp) {
            console.log('🔄 更新字幕按钮状态');

            // 检查当前字幕状态
            var hasVisibleSubtitles = false;
            var subtitleInfo = [];

            if (dp.video.textTracks && dp.video.textTracks.length > 0) {
                for (var i = 0; i < dp.video.textTracks.length; i++) {
                    var track = dp.video.textTracks[i];
                    var trackInfo = {
                        index: i,
                        label: track.label,
                        mode: track.mode,
                        cues: track.cues ? track.cues.length : 0,
                        readyState: track.readyState
                    };
                    subtitleInfo.push(trackInfo);

                    // 检查是否有可见字幕
                    if (track.mode === 'showing') {
                        hasVisibleSubtitles = true;
                    }
                }
            }

            console.log('📊 字幕轨道信息:', subtitleInfo);
            console.log('👁️ 当前字幕可见状态:', hasVisibleSubtitles);

            // 直接更新按钮样式 - 使用统一的选择器
            var subtitleButton = dp.container.querySelector('.dplayer-subtitle-btn') ||
                                dp.container.querySelector('.dplayer-subtitle-button') ||
                                dp.container.querySelector('[data-balloon="字幕"]') ||
                                dp.container.querySelector('.dplayer-icon-subtitle');

            if (subtitleButton) {
                if (hasVisibleSubtitles) {
                    subtitleButton.classList.add('dplayer-subtitle-btn-active');
                    subtitleButton.style.opacity = '1';
                    console.log('✅ 字幕按钮设置为激活状态');
                } else {
                    subtitleButton.classList.remove('dplayer-subtitle-btn-active');
                    subtitleButton.style.opacity = '0.8';
                    console.log('✅ 字幕按钮设置为非激活状态');
                }
            } else {
                console.warn('⚠️ 未找到字幕按钮，尝试查找所有可能的按钮');
                // 调试：列出所有可能的按钮
                var allButtons = dp.container.querySelectorAll('button, .dplayer-icon');
                console.log('所有按钮:', Array.from(allButtons).map(btn => ({
                    className: btn.className,
                    innerHTML: btn.innerHTML,
                    title: btn.title,
                    dataset: btn.dataset
                })));
            }

            // 同时触发DPlayer事件（保持兼容性）
            if (hasVisibleSubtitles) {
                dp.events.trigger('subtitle_show');
            } else {
                dp.events.trigger('subtitle_hide');
            }
        }

        // 切换字幕显示状态
        function toggleSubtitleDisplay(dp) {
            console.log('🔄 切换字幕显示状态');

            // 检查当前字幕状态
            var hasVisibleSubtitles = false;
            var subtitleTracks = [];
            var availableTracks = [];

            if (dp.video.textTracks && dp.video.textTracks.length > 0) {
                for (var i = 0; i < dp.video.textTracks.length; i++) {
                    var track = dp.video.textTracks[i];
                    var trackInfo = {
                        index: i,
                        label: track.label,
                        mode: track.mode,
                        cues: track.cues ? track.cues.length : 0,
                        readyState: track.readyState
                    };
                    subtitleTracks.push(trackInfo);

                    // 检查轨道是否可用（有内容或者已加载）
                    if (track.cues && track.cues.length > 0) {
                        availableTracks.push(trackInfo);
                        if (track.mode === 'showing') {
                            hasVisibleSubtitles = true;
                        }
                    } else if (track.readyState === 2) { // LOADED
                        availableTracks.push(trackInfo);
                        if (track.mode === 'showing') {
                            hasVisibleSubtitles = true;
                        }
                    }
                }
            }

            console.log('📊 当前字幕轨道状态:', subtitleTracks);
            console.log('📋 可用字幕轨道:', availableTracks);
            console.log('👁️ 当前字幕可见状态:', hasVisibleSubtitles);

            // 如果没有可用的字幕轨道，尝试查找动态添加的字幕
            if (availableTracks.length === 0) {
                console.log('🔍 没有找到可用字幕轨道，检查是否有动态添加的字幕');

                // 查找所有track元素
                var trackElements = dp.video.querySelectorAll('track');
                console.log('📝 找到track元素数量:', trackElements.length);

                if (trackElements.length > 0) {
                    // 强制启用第一个track
                    var firstTrack = trackElements[0];
                    if (firstTrack.track) {
                        firstTrack.track.mode = hasVisibleSubtitles ? 'disabled' : 'showing';
                        console.log('🎯 强制设置第一个track模式:', firstTrack.track.mode);

                        if (firstTrack.track.mode === 'showing') {
                            dp.notice('字幕已显示', 1000);
                            dp.events.trigger('subtitle_show');
                        } else {
                            dp.notice('字幕已隐藏', 1000);
                            dp.events.trigger('subtitle_hide');
                        }
                        return;
                    }
                }
            }

            // 切换字幕显示状态
            if (hasVisibleSubtitles) {
                // 隐藏所有字幕
                for (var i = 0; i < dp.video.textTracks.length; i++) {
                    dp.video.textTracks[i].mode = 'disabled';
                }
                console.log('🙈 字幕已隐藏');
                dp.notice('字幕已隐藏', 1000);
                // 触发字幕隐藏事件
                dp.events.trigger('subtitle_hide');
            } else {
                // 显示字幕
                var hasEnabledAny = false;
                for (var i = 0; i < availableTracks.length; i++) {
                    var trackInfo = availableTracks[i];
                    var track = dp.video.textTracks[trackInfo.index];
                    track.mode = 'showing';
                    hasEnabledAny = true;
                    console.log('👁️ 启用字幕轨道:', track.label);
                    break; // 只启用第一个可用的轨道
                }

                if (hasEnabledAny) {
                    console.log('👁️ 字幕已显示');
                    dp.notice('字幕已显示', 1000);
                    // 触发字幕显示事件
                    dp.events.trigger('subtitle_show');
                } else {
                    console.log('⚠️ 没有可用的字幕轨道');
                    dp.notice('没有可用的字幕', 1000);
                    // 触发字幕隐藏事件
                    dp.events.trigger('subtitle_hide');
                }
            }

            // 打印切换后的状态
            setTimeout(function() {
                var newSubtitleTracks = [];
                for (var i = 0; i < dp.video.textTracks.length; i++) {
                    var track = dp.video.textTracks[i];
                    newSubtitleTracks.push({
                        index: i,
                        label: track.label,
                        mode: track.mode,
                        cues: track.cues ? track.cues.length : 0
                    });
                }
                console.log('📊 切换后字幕轨道状态:', newSubtitleTracks);
            }, 100);
        }

        function initDPlayer(m3u8, subtitles) {

            // 处理字幕配置
            var subtitleConfig = null;
            if (subtitles && subtitles.length > 0) {
                // 根据用户设置选择默认字幕
                var defaultSubtitle = G.get('default_subtitle');
                var selectedSubtitle = null;

                if (defaultSubtitle === '自动选择') {
                    // 优先选择中文字幕
                    selectedSubtitle = subtitles.find(sub => sub.lang.includes('zh')) || subtitles[0];
                } else if (defaultSubtitle === '中文') {
                    selectedSubtitle = subtitles.find(sub => sub.lang.includes('zh'));
                } else if (defaultSubtitle === '英文') {
                    selectedSubtitle = subtitles.find(sub => sub.lang.includes('en'));
                }

                if (selectedSubtitle) {
                    subtitleConfig = {
                        url: selectedSubtitle.url,
                        type: selectedSubtitle.type,
                        fontSize: '20px',
                        bottom: '40px',
                        color: '#fff'
                    };
                }

                console.log('字幕配置:', subtitleConfig);
                console.log('可用字幕:', subtitles);
            }

            var dp = new DPlayer({
                container: $('#Dplayer')[0],
                screenshot: true,
                volume: 1,
                video: {
                    quality: m3u8,
                    defaultQuality: z,
                },
                subtitle: subtitleConfig || {
                    url: 'data:text/vtt;charset=utf-8,WEBVTT%0A%0A00%3A00%3A01.000%20--%3E%2000%3A00%3A02.000%0A%E5%AD%97%E5%B9%95%E5%8A%A0%E8%BD%BD%E4%B8%AD...',
                    type: 'webvtt'
                },
                contextmenu: [
                    {
                        text: '下载视频',
                        click: function(t) {
                            var key = { pc: pickID, fid: videoID };
                            download(key).then(function(data) {
                                if (data[0]) {
                                    GM_openInTab(data[0]);
                                    if (G.get('show_sha')) {
                                        setTimeout(function() {
                                            prompt('文件下载中，校验码(SHA1)为：', sha);
                                        }, 1000);
                                    }
                                } else {
                                    alert('下载失败！' + data[1]);
                                }
                            });
                        }
                    },
                    {
                        text: '删除视频',
                        click: function(t) {
                            dp.pause();
                            var a = confirm('确认删除 ' + titleTxt + ' 视频文件？');
                            if (a) {
                                offline.del(videoID)
                            }
                        }
                    },
                    {
                        text: '查看文件夹',
                        click: function(t) {
                            GM_openInTab(`https://115.com/?cid=${folderID}&offset=0&mode=wangpan`, false);
                        }
                    },
                    {
                        text: '删除文件夹',
                        click: function(t) {
                            var a = confirm('确认删除 ' + titleTxt + ' 视频所属文件夹？');
                            if (a) {
                                offline.del(folderID);
                            }
                        }
                    },
                    {
                        text: '设置星标',
                        click: function(t) {
                            var n = 1;
                            offline.setStar(videoID, n);
                        }
                    },
                    {
                        text: '取消星标',
                        click: function(t) {
                            var n = 0;
                            offline.setStar(videoID, n);
                        }
                    },
                    {
                        text: '重命名',
                        click: function(t) {
                            offline.newName(videoID, titleTxt);
                        }
                    },
                ].concat(subtitles && subtitles.length > 0 ? [
                    {
                        text: '字幕选择',
                        click: function(t) {
                            showSubtitleMenu(dp, subtitles);
                        }
                    }
                ] : []),
            });

            // --- LOGGING ADDED HERE ---
            console.log('DPlayer initialized with subtitleConfig:', subtitleConfig);
            console.log('Initial video textTracks:', dp.video.textTracks);
            // Print which rendering engine is being used for subtitles
            if (subtitleConfig && subtitleConfig.type) {
                console.log('Using subtitle rendering engine:', subtitleConfig.type === 'webvtt'
                    ? 'HTML5 native WebVTT'
                    : 'JavaScript SRT parser');
            } else {
                console.log('No subtitle rendering engine in use');
            }
            // Attach cuechange listener to tracks
            Array.from(dp.video.textTracks).forEach(function(track, idx) {
                track.oncuechange = function() {
                    console.log(`TextTrack ${idx} cuechange: activeCues=${this.activeCues ? this.activeCues.length : 0}`);
                };
            });
            // --------------------------

            unsafeWindow.dp = dp;
            unsafeWindow.$ = $;

            // 添加字幕按钮点击事件监听器
            setTimeout(function() {
                setupSubtitleButtonHandler(dp);
                // 初始化后立即更新按钮状态
                setTimeout(function() {
                    updateSubtitleButtonState(dp);
                }, 500);
            }, 1000);

            $('#Dplayer').click();
            $('.dplayer-menu').css('width', '98px');
            $('.dplayer-setting-loop,.dplayer-mobile-play,.dplayer-menu-item:gt(-3)').hide();
            if (m3u8.length > 1) {
                $('.dplayer-quality button').css('color', 'Lime');
            }

            $('.dplayer-quality').after(`
            <div class="dplayer-icon openPiP" data-balloon="画中画" data-balloon-pos="up">
                <span class="dplayer-icon-content"><svg width="22" height="22" viewBox="0 0 22 22" xmlns="http://www.w3.org/2000/svg"><g fill="#E6E6E6" fill-rule="evenodd"><path d="M17 4a2 2 0 012 2v6h-2V6.8a.8.8 0 00-.8-.8H4.8a.8.8 0 00-.794.7L4 6.8v8.4a.8.8 0 00.7.794l.1.006H11v2H4a2 2 0 01-2-2V6a2 2 0 012-2h13z"></path><rect x="13" y="14" width="8" height="6" rx="1"></rect></g></svg></span>
            </div>`);

            dp.on('loadstart', function() {
                dp.notice('视频加载中,请稍侯。', 1000);
            });

            var a = 0;
            dp.on('loadeddata', function() {
                dp.notice('视频加载完成。', 1000);
                a++;
                if (a != 1) return;

                // 自动加载字幕
                if (subtitles && subtitles.length > 0) {
                    console.log('自动加载字幕，可用字幕数量:', subtitles.length);

                    // 选择第一个可用的字幕
                    var firstSubtitle = subtitles[0];
                    console.log('自动加载字幕:', firstSubtitle.name, firstSubtitle.url);

                    // 延迟加载字幕，确保视频已经准备好
                    setTimeout(function() {
                        autoLoadSubtitle(dp, firstSubtitle.url);
                    }, 500);
                }

                setTimeout(function() {
                    getHistory(pickID).then(function(onTime) {
                        if (G.get('online_List') && onTime > skipTime) {
                            dp.seek(onTime);
                            dp.notice('已跳转到上次观看进度' + tranTime(onTime), 2500);
                        } else if (skipTime > 0) {
                            dp.seek(skipTime);
                            dp.notice('已跳过片头' + skipTime + '秒', 2500);
                        }

                        if (document.hidden && G.get('Tab_ing')) {
                            return;
                        }
                        dp.play();
                    });

                }, 1000);

            });

            var b = 0;
            dp.on('timeupdate', function() {
                if ((dp.video.duration - dp.video.currentTime) <= (skipTime2 > 0 ? skipTime2 : 30)) {
                    var ed = 1;
                    b++
                    if (skipTime2 > 0) {
                        dp.notice('已跳过片尾' + skipTime2 + '秒', 2500);
                        dp.pause();
                        if (b % 2 == 0) {
                            setTimeout(function() {
                                alert('视频已播放结束！');
                            }, 1000);
                        }
                    }
                } else {
                    var ed = 0;
                }
                GM_setValue('end', ed);
            });

            var c = 0;
            var up;
            function upTime(out) {
                up = setInterval(function() {
                    var end = GM_getValue('end') || 0;
                    var newTime = dp.video.currentTime.toFixed(0);
                    var t = parseInt(dp.video.currentTime - c);
                    c = dp.video.currentTime;

                    var key = {
                        'op': 'update',
                        'pick_code': pickID,
                        'time': end ? 0 : newTime,
                        'definition': end,
                        'category': 1
                    };
                    var history_url = 'https://webapi.115.com/files/history';
                    if (end || (c >= 30 && Math.abs(t) > 1)) {
                        offline.getData(history_url, $.param(key)).then(function(json) {
                            json.state ? console.log('上传播放记录成功！') : console.log('上传播放记录失败，' + json.error);
                        });

                    }

                    if (dp.video.paused || dp.video.error || end) {
                        GM_setValue('stop', true);
                        clearInterval(up);
                    }

                }, out);
            }

            if (G.get('online_List')) {
                dp.on('play', function() {
                    var stop = GM_getValue('stop');
                    if (stop) {
                        GM_setValue('stop', false);
                        upTime(3000);
                    }
                });

                dp.on('seeked', function() {
                    if (dp.video.paused) {
                        upTime(50);
                    }
                });
            }


            dp.on('error', function() {
                alert('视频加载失败！');
            });

            dp.on('ended', function() {
                alert('视频播放结束！');
            });

            // 监听视频数据加载完成事件，用于更新字幕按钮状态
            dp.on('loadeddata', function() {
                console.log('📹 视频数据加载完成，更新字幕按钮状态');
                setTimeout(function() {
                    updateSubtitleButtonState(dp);
                }, 500);
            });
        }
        playVideo(m3u8);
        autobox();
    }

    // 页面加载完成后的初始化
    $(document).ready(function() {
        // 窗口大小改变时自动调整播放器
        $(window).resize(function() {
            if (localHref.indexOf('https://videotsgo.115.com') != -1 || localHref.indexOf('https://115.com/web/lixian') != -1) {
                autobox();
            }
        });

        // 页面可见性变化处理
        $(document).on('visibilitychange click', function(e) {
            if (e.type == 'click') {
                return;
            }

            var isHidden = e.target.hidden;
            if (localHref.match(/115\.com\S*(lixian|play)|v\.anxia\.com|videotsgo\.115\.com/) != null &&
                G.get('Tab_ing') && !document.pictureInPictureElement) {

                isHidden ? $('video')[0].pause() : $('video')[0].play();

            }
        });

        // 画中画按钮事件处理
        $('body').on('click mouseenter mouseleave', '.openPiP', function(e) {
            if (e.type == 'click') {
                enterPiP($('video')[0]);
            } else if (e.type == 'mouseenter') {
                $(this).css('opacity', 1);
            } else if (e.type == 'mouseleave') {
                $(this).css('opacity', 0.7);
            }
            return false;
        });

        // 播放按钮事件处理
        $('body').on('click', '.Dp', function() {
            var type = $(this).attr('class').replace(/name\s?/g, '');

            if ($(this).is('[data]')) {
                var video = JSON.parse($(this).attr('data'));
            } else {
                var $El = $(this).parents('li');
                var video = {};
                video['name'] = name2($El.attr('title'));
                video['pid'] = $El.attr('pick_code');
                video['cid'] = $El.attr('cid');
                video['fid'] = $El.attr('file_id');
                video['size'] = change($El.attr('file_size'));
                video['sha'] = $El.attr('sha1');
            }
            palyData(video, type);
            return false;
        });

        // 115网盘页面的播放按钮添加
        if (localHref.indexOf('https://115.com/') != -1) {
            var herfv = 'li[file_type="1"]:has(.duration):not([file_mode="4"],[paly_button="1"])';
            $('body').on('mouseenter', herfv, function() {
                var $El = $(this).attr('paly_button', 1);

                $El.find('.name').addClass('Dp').removeAttr('menu');

                if (!G.get('hide_play')) {
                    $El.find('.file-opr').prepend('<a href="javascript:;" class="Dp" title="使用Dplayer播放视频"><span>Dp播放</span></a>');
                }
                $El.not('.name').dblclick(function() {
                    var type = 'dblclick';
                    var pid1 = $El.attr('pick_code');
                    var video = { 'pid': pid1 };
                    palyData(video, type);
                    return false;
                });
            });
        }

        // 官方播放页面添加画中画按钮
        if (localHref.match(/115\.com\/\?ct=play|v\.anxia\.com/) != null) {
            $('.bar-side ul').prepend(`<li><a href="javascript:;" class="openPiP"
            style="float:left;width:40px;height:20px;margin:10px 5px;border-radius:3px;font-size:12px;text-align:center;background:#666;color:#fff;opacity:0.7;">
            <s>画中画</s><div class="tooltip" >开启画中画</div></a></li>`);
        }
    });

})();
