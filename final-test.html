<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>字幕按钮修复验证</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/dplayer@1.27.1/dist/DPlayer.min.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #000;
            color: #fff;
            font-family: Arial, sans-serif;
        }
        #Dplayer {
            width: 100%;
            height: 400px;
            margin: 20px 0;
        }
        .info {
            background: #111;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 14px;
            max-height: 200px;
            overflow-y: auto;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            background: #333;
            color: #fff;
            border: 1px solid #555;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #555;
        }
        .success {
            color: #0f0;
        }
        .error {
            color: #f00;
        }
    </style>
</head>
<body>
    <h1>字幕按钮修复验证</h1>
    
    <div class="info">
        <div>🎯 <strong>验证目标</strong>：字幕按钮状态与实际字幕显示状态同步</div>
        <div>✅ <strong>期望结果</strong>：</div>
        <div>&nbsp;&nbsp;1. 字幕显示时，按钮显示为"激活状态"（可点击隐藏）</div>
        <div>&nbsp;&nbsp;2. 字幕隐藏时，按钮显示为"非激活状态"（可点击显示）</div>
        <div>&nbsp;&nbsp;3. 点击按钮能正确切换字幕显示/隐藏</div>
    </div>
    
    <div id="Dplayer"></div>
    
    <div>
        <button onclick="loadSubtitle()">加载字幕</button>
        <button onclick="checkStatus()">检查状态</button>
        <button onclick="testToggle()">测试切换</button>
        <button onclick="setupSubtitleButtonHandler()">重新设置按钮</button>
        <button onclick="clearLog()">清空日志</button>
    </div>
    
    <div class="info" id="log">等待操作...</div>

    <script src="https://cdn.jsdelivr.net/npm/dplayer@1.27.1/dist/DPlayer.min.js"></script>
    <script>
        function log(msg, type = '') {
            const logDiv = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            const className = type ? ` class="${type}"` : '';
            logDiv.innerHTML += `<div${className}>[${time}] ${msg}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(msg);
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '日志已清空<br>';
        }

        // 测试字幕内容
        const testSubtitle = `WEBVTT

00:00:01.000 --> 00:00:05.000
🎬 字幕按钮状态修复测试

00:00:06.000 --> 00:00:10.000
✨ 验证按钮状态同步功能

00:00:11.000 --> 00:00:15.000
🔄 测试切换功能是否正常

00:00:16.000 --> 00:00:20.000
✅ 修复验证完成`;

        // 初始化DPlayer - 使用修复后的配置
        const dp = new DPlayer({
            container: document.getElementById('Dplayer'),
            screenshot: true,
            volume: 0.7,
            video: {
                url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
                pic: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/images/BigBuckBunny.jpg'
            },
            subtitle: {
                url: 'data:text/vtt;charset=utf-8,WEBVTT%0A%0A00%3A00%3A01.000%20--%3E%2000%3A00%3A02.000%0A%E5%AD%97%E5%B9%95%E5%8A%A0%E8%BD%BD%E4%B8%AD...',
                type: 'webvtt'
            }
        });

        log('DPlayer 初始化完成，使用修复后的字幕配置');

        // 监听DPlayer事件
        dp.on('loadeddata', function() {
            log('📹 视频数据加载完成');
            setTimeout(function() {
                checkStatus();
                updateSubtitleButtonState();
            }, 1000);
        });

        dp.events.on('subtitle_show', function() {
            log('🎯 DPlayer事件: subtitle_show 被触发', 'success');
        });

        dp.events.on('subtitle_hide', function() {
            log('🎯 DPlayer事件: subtitle_hide 被触发', 'success');
        });

        // 更新字幕按钮状态 - 使用DPlayer事件系统
        function updateSubtitleButtonState() {
            log('🔄 更新字幕按钮状态');
            
            // 检查当前字幕状态
            var hasVisibleSubtitles = false;
            if (dp.video.textTracks && dp.video.textTracks.length > 0) {
                for (var i = 0; i < dp.video.textTracks.length; i++) {
                    var track = dp.video.textTracks[i];
                    if (track.mode === 'showing' && track.cues && track.cues.length > 0) {
                        hasVisibleSubtitles = true;
                        break;
                    }
                }
            }
            
            log(`👁️ 当前字幕可见状态: ${hasVisibleSubtitles}`);
            
            // 使用DPlayer的事件系统来更新按钮状态
            if (hasVisibleSubtitles) {
                dp.events.trigger('subtitle_show');
                log('✅ 触发 subtitle_show 事件', 'success');
            } else {
                dp.events.trigger('subtitle_hide');
                log('✅ 触发 subtitle_hide 事件', 'success');
            }
        }

        // 设置字幕按钮处理器 - 修复点击功能
        function setupSubtitleButtonHandler() {
            log('🎬 设置字幕按钮处理器');

            var subtitleButton = document.querySelector('.dplayer-subtitle-button');
            if (!subtitleButton) {
                log('⚠️ 未找到字幕按钮，延迟重试');
                setTimeout(setupSubtitleButtonHandler, 1000);
                return;
            }

            log(`✅ 找到字幕按钮: ${subtitleButton.className}`);

            // 首先更新按钮状态
            updateSubtitleButtonState();

            // 移除DPlayer原生的点击事件监听器，添加我们自定义的
            var newButton = subtitleButton.cloneNode(true);
            subtitleButton.parentNode.replaceChild(newButton, subtitleButton);

            // 添加自定义的点击事件监听器
            newButton.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                log('🎯 字幕按钮被点击');
                toggleSubtitleDisplay();

                // 延迟更新按钮状态
                setTimeout(function() {
                    updateSubtitleButtonState();
                    checkStatus();
                }, 100);
            });

            log('✅ 字幕按钮自定义事件监听器已设置');
            dp._customSubtitleButton = newButton;
        }

        // 切换字幕显示状态
        function toggleSubtitleDisplay() {
            log('🔄 切换字幕显示状态');

            // 检查当前字幕状态
            var hasVisibleSubtitles = false;
            var subtitleTracks = [];

            if (dp.video.textTracks && dp.video.textTracks.length > 0) {
                for (var i = 0; i < dp.video.textTracks.length; i++) {
                    var track = dp.video.textTracks[i];
                    subtitleTracks.push({
                        index: i,
                        label: track.label,
                        mode: track.mode,
                        cues: track.cues ? track.cues.length : 0
                    });

                    if (track.mode === 'showing') {
                        hasVisibleSubtitles = true;
                    }
                }
            }

            log(`📊 当前字幕轨道状态: ${JSON.stringify(subtitleTracks)}`);
            log(`👁️ 当前字幕可见状态: ${hasVisibleSubtitles}`);

            // 切换字幕显示状态
            if (hasVisibleSubtitles) {
                // 隐藏所有字幕
                for (var i = 0; i < dp.video.textTracks.length; i++) {
                    dp.video.textTracks[i].mode = 'disabled';
                }
                log('🙈 字幕已隐藏');
                // 触发字幕隐藏事件
                dp.events.trigger('subtitle_hide');
            } else {
                // 显示字幕
                var hasEnabledAny = false;
                for (var i = 0; i < dp.video.textTracks.length; i++) {
                    var track = dp.video.textTracks[i];
                    if (track.cues && track.cues.length > 0) {
                        track.mode = 'showing';
                        hasEnabledAny = true;
                        log(`👁️ 启用字幕轨道: ${track.label}`);
                        break; // 只启用第一个有内容的轨道
                    }
                }

                if (hasEnabledAny) {
                    log('👁️ 字幕已显示');
                    // 触发字幕显示事件
                    dp.events.trigger('subtitle_show');
                } else {
                    log('⚠️ 没有可用的字幕轨道');
                    // 触发字幕隐藏事件
                    dp.events.trigger('subtitle_hide');
                }
            }
        }

        function loadSubtitle() {
            log('🚀 开始加载测试字幕');
            
            // 清理现有字幕
            if (dp.video.textTracks) {
                for (var i = 0; i < dp.video.textTracks.length; i++) {
                    dp.video.textTracks[i].mode = 'disabled';
                }
            }
            var existingTracks = dp.video.querySelectorAll('track');
            existingTracks.forEach(function(track) {
                track.remove();
            });
            
            // 创建新的字幕轨道
            const dataUrl = 'data:text/vtt;charset=utf-8,' + encodeURIComponent(testSubtitle);
            const track = document.createElement('track');
            track.kind = 'subtitles';
            track.src = dataUrl;
            track.srclang = 'zh-CN';
            track.label = '测试字幕';
            track.default = true;
            
            dp.video.appendChild(track);
            
            track.addEventListener('load', function() {
                log('✅ 字幕轨道加载完成');
                track.mode = 'showing';
                
                setTimeout(function() {
                    if (track.cues && track.cues.length > 0) {
                        log(`📝 字幕内容加载成功，cues数量: ${track.cues.length}`);
                        log(`🎯 字幕轨道模式: ${track.mode}`);
                        
                        // 设置按钮处理器和更新状态
                        setTimeout(function() {
                            setupSubtitleButtonHandler();
                            checkStatus();
                        }, 500);
                    } else {
                        log('⚠️ 字幕轨道无内容', 'error');
                    }
                }, 500);
            });
            
            // 立即设置为显示模式
            setTimeout(function() {
                track.mode = 'showing';
                log(`🎯 字幕已启用，模式: ${track.mode}`);
            }, 100);
        }

        function checkStatus() {
            log('🔍 检查字幕按钮状态');
            
            const button = document.querySelector('.dplayer-subtitle-button');
            if (button) {
                const inner = button.querySelector('.dplayer-subtitle-button-inner');
                const balloon = button.getAttribute('data-balloon');
                const opacity = inner ? inner.style.opacity : '1';
                
                log(`✅ 字幕按钮存在`);
                log(`🎨 按钮内部透明度: ${opacity}`);
                log(`💬 提示文本: ${balloon}`);
                
                // 检查实际字幕状态
                let actualVisible = false;
                let trackCount = 0;
                if (dp.video.textTracks && dp.video.textTracks.length > 0) {
                    for (let i = 0; i < dp.video.textTracks.length; i++) {
                        const track = dp.video.textTracks[i];
                        trackCount++;
                        if (track.mode === 'showing' && track.cues && track.cues.length > 0) {
                            actualVisible = true;
                            log(`📝 轨道 ${i}: ${track.label}, 模式: ${track.mode}, cues: ${track.cues.length}`);
                        }
                    }
                }
                
                log(`📊 字幕轨道总数: ${trackCount}`);
                log(`👁️ 实际字幕可见: ${actualVisible}`);
                
                // 判断状态是否一致
                const buttonShowsActive = (opacity !== '0.4' && balloon === 'hide-subs');
                const buttonShowsInactive = (opacity === '0.4' || balloon === 'show-subs');
                
                log(`🎯 按钮显示激活状态: ${buttonShowsActive}`);
                log(`🎯 按钮显示非激活状态: ${buttonShowsInactive}`);
                
                const stateConsistent = (actualVisible && buttonShowsActive) || (!actualVisible && buttonShowsInactive);
                
                if (stateConsistent) {
                    log('✅ 按钮状态与实际状态一致！', 'success');
                } else {
                    log('❌ 按钮状态与实际状态不一致！', 'error');
                    log(`期望: 字幕${actualVisible ? '显示' : '隐藏'} -> 按钮应显示${actualVisible ? '激活' : '非激活'}状态`, 'error');
                    log(`实际: 按钮显示${buttonShowsActive ? '激活' : '非激活'}状态`, 'error');
                }
            } else {
                log('❌ 字幕按钮不存在', 'error');
                
                // 检查所有控制按钮
                const allIcons = document.querySelectorAll('.dplayer-icons > *');
                log(`🔍 控制栏按钮总数: ${allIcons.length}`);
                Array.from(allIcons).forEach(function(icon, index) {
                    log(`按钮 ${index}: ${icon.className}`);
                });
            }
        }

        function testToggle() {
            log('🔧 手动测试字幕切换');
            toggleSubtitleDisplay();
            setTimeout(function() {
                checkStatus();
            }, 200);
        }

        // 延迟初始检查
        setTimeout(function() {
            log('🚀 开始初始检查');
            checkStatus();
        }, 3000);
    </script>
</body>
</html>
