<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>字幕功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #0056b3;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>115云盘字幕功能测试</h1>
        
        <div class="test-section">
            <h3>测试说明</h3>
            <p>这个页面用于测试115云盘DPlayer字幕功能的修复情况。</p>
            <p>请在115云盘视频播放页面的控制台中运行以下测试。</p>
        </div>

        <div class="test-section">
            <h3>测试步骤</h3>
            <ol>
                <li>打开115云盘视频播放页面</li>
                <li>打开浏览器开发者工具（F12）</li>
                <li>在控制台中复制粘贴下面的测试代码</li>
                <li>观察控制台输出和字幕按钮行为</li>
            </ol>
        </div>

        <div class="test-section">
            <h3>测试代码</h3>
            <button class="button" onclick="copyTestCode()">复制测试代码</button>
            <div class="log" id="testCode">
// 115云盘字幕功能测试代码
console.log('🧪 开始字幕功能测试...');

// 查找DPlayer实例
var dp = window.dp || window.dplayer;
if (!dp) {
    console.error('❌ 未找到DPlayer实例');
} else {
    console.log('✅ 找到DPlayer实例');
    
    // 测试1: 检查字幕轨道
    console.log('📊 测试1: 检查字幕轨道');
    if (dp.video.textTracks && dp.video.textTracks.length > 0) {
        for (var i = 0; i < dp.video.textTracks.length; i++) {
            var track = dp.video.textTracks[i];
            console.log(`轨道 ${i}:`, {
                label: track.label,
                mode: track.mode,
                cues: track.cues ? track.cues.length : 0,
                readyState: track.readyState
            });
        }
    } else {
        console.log('⚠️ 没有找到字幕轨道');
    }
    
    // 测试2: 检查字幕按钮
    console.log('📊 测试2: 检查字幕按钮');
    var subtitleButton = dp.container.querySelector('.dplayer-subtitle-btn') || 
                        dp.container.querySelector('.dplayer-subtitle-button') ||
                        dp.container.querySelector('[data-balloon="字幕"]') ||
                        dp.container.querySelector('.dplayer-icon-subtitle');
    
    if (subtitleButton) {
        console.log('✅ 找到字幕按钮:', subtitleButton.className);
        console.log('按钮样式:', {
            opacity: subtitleButton.style.opacity,
            color: getComputedStyle(subtitleButton).color,
            backgroundColor: getComputedStyle(subtitleButton).backgroundColor
        });
        
        // 测试3: 模拟点击字幕按钮
        console.log('📊 测试3: 模拟点击字幕按钮');
        console.log('点击前字幕状态:');
        if (dp.video.textTracks && dp.video.textTracks.length > 0) {
            for (var i = 0; i < dp.video.textTracks.length; i++) {
                var track = dp.video.textTracks[i];
                console.log(`轨道 ${i} 状态:`, {
                    mode: track.mode,
                    cues: track.cues ? track.cues.length : 0
                });
            }
        }
        
        // 点击按钮
        subtitleButton.click();
        
        setTimeout(function() {
            console.log('点击后字幕状态:');
            if (dp.video.textTracks && dp.video.textTracks.length > 0) {
                for (var i = 0; i < dp.video.textTracks.length; i++) {
                    var track = dp.video.textTracks[i];
                    console.log(`轨道 ${i} 状态:`, {
                        mode: track.mode,
                        cues: track.cues ? track.cues.length : 0
                    });
                }
            }
        }, 500);
        
    } else {
        console.log('❌ 未找到字幕按钮');
        // 列出所有可能的按钮
        var allButtons = dp.container.querySelectorAll('button, .dplayer-icon');
        console.log('所有按钮:', Array.from(allButtons).map(btn => ({
            className: btn.className,
            innerHTML: btn.innerHTML.substring(0, 50),
            title: btn.title
        })));
    }
}

console.log('🧪 测试完成');
            </div>
        </div>

        <div class="test-section">
            <h3>预期结果</h3>
            <div class="status success">
                <strong>成功情况：</strong>
                <ul>
                    <li>控制台显示找到DPlayer实例</li>
                    <li>显示字幕轨道信息</li>
                    <li>找到字幕按钮</li>
                    <li>点击按钮后字幕状态发生变化</li>
                    <li>字幕按钮样式发生变化</li>
                </ul>
            </div>
            <div class="status error">
                <strong>失败情况：</strong>
                <ul>
                    <li>未找到DPlayer实例</li>
                    <li>未找到字幕按钮</li>
                    <li>点击按钮后字幕状态无变化</li>
                    <li>控制台出现错误信息</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <h3>故障排除</h3>
            <div class="status warning">
                <strong>如果测试失败：</strong>
                <ol>
                    <li>确保已安装最新版本的用户脚本</li>
                    <li>刷新页面重新加载脚本</li>
                    <li>检查控制台是否有JavaScript错误</li>
                    <li>确保视频已开始播放</li>
                    <li>检查是否有字幕文件可用</li>
                </ol>
            </div>
        </div>
    </div>

    <script>
        function copyTestCode() {
            var testCode = document.getElementById('testCode').textContent;
            navigator.clipboard.writeText(testCode).then(function() {
                alert('测试代码已复制到剪贴板！');
            }).catch(function(err) {
                console.error('复制失败:', err);
                alert('复制失败，请手动选择代码复制');
            });
        }
    </script>
</body>
</html>
