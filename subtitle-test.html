<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>字幕测试</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/dplayer@1.27.1/dist/DPlayer.min.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #000;
            color: #fff;
            font-family: Arial, sans-serif;
        }
        #Dplayer {
            width: 100%;
            height: 500px;
            margin: 20px 0;
        }
        .controls {
            margin: 20px 0;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            background: #333;
            color: #fff;
            border: 1px solid #555;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #555;
        }
        .log {
            background: #111;
            padding: 10px;
            border-radius: 4px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>DPlayer 字幕功能测试</h1>
    
    <div id="Dplayer"></div>
    
    <div class="controls">
        <button onclick="loadSubtitle1()">加载字幕1</button>
        <button onclick="loadSubtitle2()">加载字幕2</button>
        <button onclick="clearSubtitles()">清除字幕</button>
        <button onclick="toggleSubtitles()">切换字幕显示</button>
        <button onclick="clearLog()">清除日志</button>
    </div>
    
    <div class="log" id="log"></div>

    <script src="https://cdn.jsdelivr.net/npm/dplayer@1.27.1/dist/DPlayer.min.js"></script>
    <script>
        // 日志函数
        function log(message) {
            const logDiv = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${time}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        // 创建测试字幕内容
        const testSubtitle1 = `WEBVTT

00:00:01.000 --> 00:00:03.000
这是第一条测试字幕

00:00:04.000 --> 00:00:06.000
这是第二条测试字幕

00:00:07.000 --> 00:00:09.000
字幕功能测试中...`;

        const testSubtitle2 = `WEBVTT

00:00:02.000 --> 00:00:04.000
这是另一个字幕文件

00:00:05.000 --> 00:00:07.000
用于测试字幕切换功能

00:00:08.000 --> 00:00:10.000
测试完成！`;

        // 初始化DPlayer
        const dp = new DPlayer({
            container: document.getElementById('Dplayer'),
            screenshot: true,
            volume: 0.7,
            video: {
                url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
                pic: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/images/BigBuckBunny.jpg'
            },
            subtitle: {
                url: '',
                type: 'webvtt'
            }
        });

        log('DPlayer 初始化完成');

        // 设置字幕按钮处理器
        setTimeout(function() {
            setupSubtitleButtonHandler(dp);
        }, 2000);

        // 字幕处理函数
        function processSubtitleContent(subtitleText) {
            log('开始处理字幕内容，长度: ' + subtitleText.length);
            
            // 保存当前播放状态
            const wasPlaying = !dp.video.paused;
            const currentTime = dp.video.currentTime;
            
            // 清理现有字幕
            clearExistingSubtitles();
            
            // 创建data URL
            const dataUrl = 'data:text/vtt;charset=utf-8,' + encodeURIComponent(subtitleText);
            
            // 创建新的字幕轨道
            const track = document.createElement('track');
            track.kind = 'subtitles';
            track.src = dataUrl;
            track.srclang = 'zh-CN';
            track.label = '测试字幕';
            track.default = true;
            
            // 添加到video元素
            dp.video.appendChild(track);
            
            // 等待轨道加载完成后启用
            track.addEventListener('load', function() {
                log('字幕轨道加载完成');
                track.mode = 'showing';
                
                setTimeout(function() {
                    if (track.cues && track.cues.length > 0) {
                        log('字幕加载成功，cues数量: ' + track.cues.length);
                        
                        // 恢复播放状态
                        if (wasPlaying) {
                            dp.video.currentTime = currentTime;
                            dp.play();
                        }
                    } else {
                        log('警告：字幕轨道无内容');
                    }
                }, 500);
            });
            
            track.addEventListener('error', function(e) {
                log('错误：字幕轨道加载失败 - ' + e.message);
                
                // 恢复播放状态
                if (wasPlaying) {
                    dp.video.currentTime = currentTime;
                    dp.play();
                }
            });
            
            // 立即设置为显示模式
            setTimeout(function() {
                track.mode = 'showing';
                log('字幕已启用，模式: ' + track.mode);
                
                // 如果load事件没有触发，也要恢复播放状态
                setTimeout(function() {
                    if (wasPlaying && dp.video.paused) {
                        dp.video.currentTime = currentTime;
                        dp.play();
                    }
                }, 1000);
            }, 100);
        }

        function clearExistingSubtitles() {
            try {
                // 禁用所有现有字幕轨道
                if (dp.video.textTracks) {
                    for (let i = 0; i < dp.video.textTracks.length; i++) {
                        dp.video.textTracks[i].mode = 'disabled';
                    }
                }

                // 移除现有的track元素
                const existingTracks = dp.video.querySelectorAll('track');
                existingTracks.forEach(function(track) {
                    track.remove();
                });
                
                log('已清理现有字幕轨道');
            } catch (error) {
                log('清理字幕轨道时出错: ' + error.message);
            }
        }

        // 测试函数
        function loadSubtitle1() {
            log('加载测试字幕1');
            processSubtitleContent(testSubtitle1);
        }

        function loadSubtitle2() {
            log('加载测试字幕2');
            processSubtitleContent(testSubtitle2);
        }

        function clearSubtitles() {
            log('清除所有字幕');
            clearExistingSubtitles();
        }

        function toggleSubtitles() {
            if (dp.video.textTracks && dp.video.textTracks.length > 0) {
                const track = dp.video.textTracks[0];
                if (track.mode === 'showing') {
                    track.mode = 'disabled';
                    log('字幕已隐藏');
                } else {
                    track.mode = 'showing';
                    log('字幕已显示');
                }
            } else {
                log('没有可用的字幕轨道');
            }
        }

        // 监听视频事件
        dp.on('loadeddata', function() {
            log('视频加载完成');
        });

        dp.on('play', function() {
            log('视频开始播放');
        });

        dp.on('pause', function() {
            log('视频暂停');
        });

        // 设置字幕按钮处理器
        function setupSubtitleButtonHandler(dp) {
            log('🎬 设置字幕按钮处理器');

            // 查找字幕按钮 - 使用正确的选择器
            var subtitleButton = document.querySelector('.dplayer-subtitle-button');
            if (!subtitleButton) {
                // 如果没有找到，尝试其他可能的选择器
                subtitleButton = document.querySelector('.dplayer-subtitle-btn');
                if (!subtitleButton) {
                    subtitleButton = document.querySelector('[data-balloon*="字幕"]');
                    if (!subtitleButton) {
                        subtitleButton = document.querySelector('[data-balloon*="show-subs"]');
                        if (!subtitleButton) {
                            subtitleButton = document.querySelector('[data-balloon*="hide-subs"]');
                        }
                    }
                }
            }

            if (subtitleButton) {
                log('✅ 找到字幕按钮: ' + subtitleButton.className);

                // 移除原有的事件监听器（如果有的话）
                var newButton = subtitleButton.cloneNode(true);
                subtitleButton.parentNode.replaceChild(newButton, subtitleButton);

                // 添加新的点击事件监听器
                newButton.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    log('🎯 字幕按钮被点击');
                    toggleSubtitleDisplay(dp);
                });

                log('✅ 字幕按钮事件监听器已设置');
            } else {
                log('⚠️ 未找到字幕按钮，尝试查找所有可能的按钮');

                // 打印所有可能的按钮供调试
                var allButtons = document.querySelectorAll('.dplayer-icons *');
                log('所有DPlayer图标按钮数量: ' + allButtons.length);

                // 尝试通过文本内容查找
                Array.from(allButtons).forEach(function(btn, index) {
                    log(`按钮 ${index}: ${btn.className} | ${btn.textContent} | ${btn.getAttribute('data-balloon')}`);
                });

                // 延迟重试
                setTimeout(function() {
                    setupSubtitleButtonHandler(dp);
                }, 2000);
            }
        }

        // 切换字幕显示状态
        function toggleSubtitleDisplay(dp) {
            log('🔄 切换字幕显示状态');

            // 检查当前字幕状态
            var hasVisibleSubtitles = false;
            var subtitleTracks = [];

            if (dp.video.textTracks && dp.video.textTracks.length > 0) {
                for (var i = 0; i < dp.video.textTracks.length; i++) {
                    var track = dp.video.textTracks[i];
                    subtitleTracks.push({
                        index: i,
                        label: track.label,
                        mode: track.mode,
                        cues: track.cues ? track.cues.length : 0
                    });

                    if (track.mode === 'showing') {
                        hasVisibleSubtitles = true;
                    }
                }
            }

            log('📊 当前字幕轨道状态: ' + JSON.stringify(subtitleTracks));
            log('👁️ 当前字幕可见状态: ' + hasVisibleSubtitles);

            // 切换字幕显示状态
            if (hasVisibleSubtitles) {
                // 隐藏所有字幕
                for (var i = 0; i < dp.video.textTracks.length; i++) {
                    dp.video.textTracks[i].mode = 'disabled';
                }
                log('🙈 字幕已隐藏');
            } else {
                // 显示字幕
                var hasEnabledAny = false;
                for (var i = 0; i < dp.video.textTracks.length; i++) {
                    var track = dp.video.textTracks[i];
                    if (track.cues && track.cues.length > 0) {
                        track.mode = 'showing';
                        hasEnabledAny = true;
                        log('👁️ 启用字幕轨道: ' + track.label);
                        break; // 只启用第一个有内容的轨道
                    }
                }

                if (hasEnabledAny) {
                    log('👁️ 字幕已显示');
                } else {
                    log('⚠️ 没有可用的字幕轨道');
                }
            }

            // 打印切换后的状态
            setTimeout(function() {
                var newSubtitleTracks = [];
                for (var i = 0; i < dp.video.textTracks.length; i++) {
                    var track = dp.video.textTracks[i];
                    newSubtitleTracks.push({
                        index: i,
                        label: track.label,
                        mode: track.mode,
                        cues: track.cues ? track.cues.length : 0
                    });
                }
                log('📊 切换后字幕轨道状态: ' + JSON.stringify(newSubtitleTracks));
            }, 100);
        }
    </script>
</body>
</html>
