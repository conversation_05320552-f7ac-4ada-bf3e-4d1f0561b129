// 115网盘DPlayer字幕功能修复脚本
// 在浏览器控制台中运行此脚本来修复字幕功能

console.log('=== 115网盘DPlayer字幕功能修复 ===');

// 检查DPlayer是否存在
if (typeof unsafeWindow === 'undefined' || typeof unsafeWindow.dp === 'undefined') {
    console.error('❌ DPlayer未找到，请确保在DPlayer播放页面运行此脚本');
} else {
    console.log('✅ 检测到DPlayer');
    
    var dp = unsafeWindow.dp;
    console.log('DPlayer对象:', dp);
    console.log('Video元素:', dp.video);
    
    // 修复字幕切换函数
    unsafeWindow.switchSubtitle = function(url) {
        console.log('🔄 切换字幕:', url);
        
        try {
            if (url) {
                // 加载指定字幕
                console.log('📥 加载字幕:', url);
                
                // 禁用所有现有字幕轨道
                if (dp.video.textTracks) {
                    for (var i = 0; i < dp.video.textTracks.length; i++) {
                        dp.video.textTracks[i].mode = 'disabled';
                    }
                    console.log('🔇 已禁用现有字幕轨道');
                }
                
                // 移除现有的track元素
                var existingTracks = dp.video.querySelectorAll('track');
                existingTracks.forEach(function(track) {
                    track.remove();
                });
                console.log('🗑️ 已移除现有track元素');
                
                // 创建新的字幕轨道
                var track = document.createElement('track');
                track.kind = 'subtitles';
                track.src = url;
                track.srclang = 'zh-CN';
                track.label = '字幕';
                track.default = true;
                
                // 添加到video元素
                dp.video.appendChild(track);
                console.log('➕ 已添加新字幕轨道');
                
                // 监听加载事件
                track.addEventListener('load', function() {
                    console.log('✅ 字幕轨道加载完成');
                    track.mode = 'showing';
                    dp.notice('字幕已加载', 2000);
                    
                    // 检查字幕内容
                    if (track.cues && track.cues.length > 0) {
                        console.log('📝 字幕内容:', track.cues.length + ' 条字幕');
                    } else {
                        console.log('⚠️ 字幕文件可能为空');
                    }
                });
                
                track.addEventListener('error', function(e) {
                    console.error('❌ 字幕加载失败:', e);
                    dp.notice('字幕加载失败', 2000);
                });
                
                // 立即设置为显示模式
                setTimeout(function() {
                    track.mode = 'showing';
                    console.log('👁️ 字幕已设置为显示模式');
                    
                    // 检查字幕状态
                    setTimeout(function() {
                        if (dp.video.textTracks.length > 0) {
                            var activeTrack = dp.video.textTracks[0];
                            console.log('📊 字幕状态:', {
                                mode: activeTrack.mode,
                                readyState: activeTrack.readyState,
                                cues: activeTrack.cues ? activeTrack.cues.length : 0
                            });
                        }
                    }, 1000);
                }, 100);
                
                dp.notice('正在加载字幕...', 1000);
                
            } else {
                // 关闭字幕
                console.log('🔇 关闭字幕');
                
                // 禁用所有字幕轨道
                if (dp.video.textTracks) {
                    for (var i = 0; i < dp.video.textTracks.length; i++) {
                        dp.video.textTracks[i].mode = 'disabled';
                    }
                }
                
                // 移除所有track元素
                var tracks = dp.video.querySelectorAll('track');
                tracks.forEach(function(track) {
                    track.remove();
                });
                
                console.log('✅ 字幕已关闭');
                dp.notice('字幕已关闭', 2000);
            }
        } catch (error) {
            console.error('❌ 字幕操作失败:', error);
            dp.notice('字幕操作失败: ' + error.message, 3000);
        }
        
        // 关闭字幕选择菜单
        if (typeof unsafeWindow.closeSubtitleMenu === 'function') {
            unsafeWindow.closeSubtitleMenu();
        } else {
            // 手动关闭菜单
            var overlay = document.getElementById('subtitle-overlay');
            if (overlay && overlay.parentElement) {
                overlay.parentElement.remove();
            }
        }
    };
    
    console.log('✅ 字幕切换函数已修复');
    
    // 测试字幕功能
    console.log('🧪 测试字幕功能...');
    
    // 创建测试字幕数据
    var testSubtitleData = 'WEBVTT\n\n00:00:01.000 --> 00:00:05.000\n测试字幕 - 修复成功！\n\n00:00:06.000 --> 00:00:10.000\n字幕功能正常工作';
    var testSubtitleUrl = 'data:text/vtt;charset=utf-8,' + encodeURIComponent(testSubtitleData);
    
    console.log('📝 测试字幕URL:', testSubtitleUrl);
    
    // 提供测试函数
    unsafeWindow.testSubtitle = function() {
        console.log('🧪 开始测试字幕...');
        unsafeWindow.switchSubtitle(testSubtitleUrl);
    };
    
    unsafeWindow.clearSubtitle = function() {
        console.log('🧹 清除字幕...');
        unsafeWindow.switchSubtitle(null);
    };
    
    console.log('✅ 字幕功能修复完成！');
    console.log('📋 可用命令:');
    console.log('  - testSubtitle(): 测试字幕功能');
    console.log('  - clearSubtitle(): 清除字幕');
    console.log('  - switchSubtitle(url): 切换到指定字幕');
    
    // 显示成功消息
    if (dp && dp.notice) {
        dp.notice('字幕功能修复完成！', 3000);
    }
}

// 如果没有unsafeWindow，提供备用方案
if (typeof unsafeWindow === 'undefined') {
    console.log('ℹ️ 请在Tampermonkey脚本环境中运行，或者手动复制修复代码到115网盘DPlayer页面的控制台中');
}
