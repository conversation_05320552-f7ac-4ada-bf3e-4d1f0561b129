<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>字幕功能修复演示</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/dplayer@1.26.0/dist/DPlayer.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .video-container {
            margin: 20px 0;
        }
        .controls {
            margin: 20px 0;
            text-align: center;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>115网盘DPlayer字幕功能修复演示</h1>
        
        <div class="status success">
            <strong>修复状态：</strong> ✅ 字幕功能已修复，使用HTML5原生TextTrack API
        </div>

        <div class="video-container">
            <div id="dplayer"></div>
        </div>

        <div class="controls">
            <button onclick="testSubtitle1()">测试字幕1</button>
            <button onclick="testSubtitle2()">测试字幕2</button>
            <button onclick="clearSubtitle()">关闭字幕</button>
            <button onclick="showSubtitleMenu()">显示字幕菜单</button>
        </div>

        <div class="status info">
            <strong>测试说明：</strong><br>
            1. 点击"测试字幕1"或"测试字幕2"按钮加载测试字幕<br>
            2. 点击"关闭字幕"按钮关闭字幕显示<br>
            3. 右键播放器选择"字幕选择"可以打开字幕选择菜单<br>
            4. 查看下方日志了解字幕加载过程
        </div>

        <div class="log" id="log"></div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/dplayer@1.26.0/dist/DPlayer.min.js"></script>
    <script>
        // 日志函数
        function log(message) {
            const logElement = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${time}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }

        // 创建测试字幕数据
        const testSubtitle1 = 'WEBVTT\n\n00:00:01.000 --> 00:00:05.000\n测试字幕1 - 修复成功！\n\n00:00:06.000 --> 00:00:10.000\n字幕功能正常工作\n\n00:00:11.000 --> 00:00:15.000\n使用HTML5原生API';
        
        const testSubtitle2 = 'WEBVTT\n\n00:00:01.000 --> 00:00:05.000\nTest Subtitle 2 - Fixed!\n\n00:00:06.000 --> 00:00:10.000\nSubtitle function works\n\n00:00:11.000 --> 00:00:15.000\nUsing HTML5 native API';

        // 创建DPlayer实例
        const dp = new DPlayer({
            container: document.getElementById('dplayer'),
            video: {
                url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
                pic: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/images/BigBuckBunny.jpg'
            },
            contextmenu: [
                {
                    text: '字幕选择',
                    click: function() {
                        showSubtitleMenu();
                    }
                }
            ]
        });

        // 修复后的字幕切换函数
        function switchSubtitle(url) {
            log('切换字幕: ' + (url || '关闭'));
            
            try {
                if (url) {
                    // 加载指定字幕
                    log('开始加载字幕...');
                    
                    // 禁用所有现有字幕轨道
                    if (dp.video.textTracks) {
                        for (let i = 0; i < dp.video.textTracks.length; i++) {
                            dp.video.textTracks[i].mode = 'disabled';
                        }
                        log('已禁用现有字幕轨道');
                    }
                    
                    // 移除现有的track元素
                    const existingTracks = dp.video.querySelectorAll('track');
                    existingTracks.forEach(track => track.remove());
                    log('已移除现有track元素');
                    
                    // 创建新的字幕轨道
                    const track = document.createElement('track');
                    track.kind = 'subtitles';
                    track.src = url;
                    track.srclang = 'zh-CN';
                    track.label = '字幕';
                    track.default = true;
                    
                    // 添加到video元素
                    dp.video.appendChild(track);
                    log('已添加新字幕轨道');
                    
                    // 监听加载事件
                    track.addEventListener('load', function() {
                        log('字幕轨道加载完成');
                        track.mode = 'showing';
                        dp.notice('字幕已加载', 2000);
                        
                        // 检查字幕内容
                        if (track.cues && track.cues.length > 0) {
                            log('字幕内容: ' + track.cues.length + ' 条字幕');
                        }
                    });
                    
                    track.addEventListener('error', function(e) {
                        log('字幕加载失败: ' + e.message);
                        dp.notice('字幕加载失败', 2000);
                    });
                    
                    // 立即设置为显示模式
                    setTimeout(function() {
                        track.mode = 'showing';
                        log('字幕已设置为显示模式');
                    }, 100);
                    
                    dp.notice('正在加载字幕...', 1000);
                    
                } else {
                    // 关闭字幕
                    log('关闭字幕');
                    
                    // 禁用所有字幕轨道
                    if (dp.video.textTracks) {
                        for (let i = 0; i < dp.video.textTracks.length; i++) {
                            dp.video.textTracks[i].mode = 'disabled';
                        }
                    }
                    
                    // 移除所有track元素
                    const tracks = dp.video.querySelectorAll('track');
                    tracks.forEach(track => track.remove());
                    
                    log('字幕已关闭');
                    dp.notice('字幕已关闭', 2000);
                }
            } catch (error) {
                log('字幕操作失败: ' + error.message);
                dp.notice('字幕操作失败: ' + error.message, 3000);
            }
        }

        // 测试函数
        function testSubtitle1() {
            const url = 'data:text/vtt;charset=utf-8,' + encodeURIComponent(testSubtitle1);
            switchSubtitle(url);
        }

        function testSubtitle2() {
            const url = 'data:text/vtt;charset=utf-8,' + encodeURIComponent(testSubtitle2);
            switchSubtitle(url);
        }

        function clearSubtitle() {
            switchSubtitle(null);
        }

        function showSubtitleMenu() {
            // 简单的字幕选择菜单
            const menu = document.createElement('div');
            menu.style.cssText = 'position:fixed;top:50%;left:50%;transform:translate(-50%,-50%);background:#000;color:#fff;padding:20px;border-radius:8px;z-index:9999;';
            menu.innerHTML = `
                <h3 style="margin:0 0 15px 0;text-align:center;">选择字幕</h3>
                <div style="margin-bottom:10px;"><button onclick="clearSubtitle();closeMenu()" style="width:100%;padding:8px;background:#333;color:#fff;border:1px solid #555;border-radius:4px;cursor:pointer;">关闭字幕</button></div>
                <div style="margin-bottom:10px;"><button onclick="testSubtitle1();closeMenu()" style="width:100%;padding:8px;background:#333;color:#fff;border:1px solid #555;border-radius:4px;cursor:pointer;">测试字幕1</button></div>
                <div style="margin-bottom:10px;"><button onclick="testSubtitle2();closeMenu()" style="width:100%;padding:8px;background:#333;color:#fff;border:1px solid #555;border-radius:4px;cursor:pointer;">测试字幕2</button></div>
                <div style="text-align:center;margin-top:15px;"><button onclick="closeMenu()" style="padding:8px 20px;background:#666;color:#fff;border:none;border-radius:4px;cursor:pointer;">取消</button></div>
            `;
            
            const overlay = document.createElement('div');
            overlay.style.cssText = 'position:fixed;top:0;left:0;width:100%;height:100%;background:rgba(0,0,0,0.5);z-index:9998;';
            overlay.onclick = closeMenu;
            
            document.body.appendChild(overlay);
            document.body.appendChild(menu);
            
            window.closeMenu = function() {
                document.body.removeChild(overlay);
                document.body.removeChild(menu);
            };
        }

        // 初始化日志
        log('DPlayer字幕功能修复演示已加载');
        log('可以开始测试字幕功能');
    </script>
</body>
</html>
