// 测试字幕功能的脚本
// 在浏览器控制台中运行此脚本来测试字幕功能

function testSubtitleFunctionality() {
    console.log('🧪 开始测试字幕功能...');
    
    // 查找DPlayer实例
    var dp = window.dp || window.dplayer;
    if (!dp) {
        console.error('❌ 未找到DPlayer实例');
        return;
    }
    
    console.log('✅ 找到DPlayer实例');
    
    // 测试1: 检查字幕轨道
    console.log('📊 测试1: 检查字幕轨道');
    if (dp.video.textTracks && dp.video.textTracks.length > 0) {
        for (var i = 0; i < dp.video.textTracks.length; i++) {
            var track = dp.video.textTracks[i];
            console.log(`轨道 ${i}:`, {
                label: track.label,
                mode: track.mode,
                cues: track.cues ? track.cues.length : 0,
                readyState: track.readyState
            });
        }
    } else {
        console.log('⚠️ 没有找到字幕轨道');
    }
    
    // 测试2: 检查字幕按钮
    console.log('📊 测试2: 检查字幕按钮');
    var subtitleButton = dp.container.querySelector('.dplayer-subtitle-btn') || 
                        dp.container.querySelector('.dplayer-subtitle-button');
    if (subtitleButton) {
        console.log('✅ 找到字幕按钮:', subtitleButton.className);
        console.log('按钮样式:', {
            opacity: subtitleButton.style.opacity,
            color: getComputedStyle(subtitleButton).color,
            backgroundColor: getComputedStyle(subtitleButton).backgroundColor
        });
    } else {
        console.log('❌ 未找到字幕按钮');
    }
    
    // 测试3: 模拟点击字幕按钮
    console.log('📊 测试3: 模拟点击字幕按钮');
    if (subtitleButton) {
        console.log('点击前字幕状态:');
        logSubtitleState(dp);
        
        subtitleButton.click();
        
        setTimeout(function() {
            console.log('点击后字幕状态:');
            logSubtitleState(dp);
        }, 500);
    }
    
    // 测试4: 检查track元素
    console.log('📊 测试4: 检查track元素');
    var trackElements = dp.video.querySelectorAll('track');
    console.log('找到track元素数量:', trackElements.length);
    trackElements.forEach(function(track, index) {
        console.log(`Track元素 ${index}:`, {
            src: track.src,
            kind: track.kind,
            label: track.label,
            default: track.default,
            readyState: track.track ? track.track.readyState : 'unknown'
        });
    });
}

function logSubtitleState(dp) {
    if (dp.video.textTracks && dp.video.textTracks.length > 0) {
        for (var i = 0; i < dp.video.textTracks.length; i++) {
            var track = dp.video.textTracks[i];
            console.log(`轨道 ${i} 状态:`, {
                mode: track.mode,
                cues: track.cues ? track.cues.length : 0,
                activeCues: track.activeCues ? track.activeCues.length : 0
            });
        }
    }
}

// 导出测试函数到全局作用域
window.testSubtitleFunctionality = testSubtitleFunctionality;
window.logSubtitleState = logSubtitleState;

console.log('🧪 字幕测试脚本已加载，运行 testSubtitleFunctionality() 开始测试');
