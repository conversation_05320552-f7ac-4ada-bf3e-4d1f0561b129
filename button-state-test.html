<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>字幕按钮状态修复测试</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/dplayer@1.27.1/dist/DPlayer.min.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #000;
            color: #fff;
            font-family: Arial, sans-serif;
        }
        #Dplayer {
            width: 100%;
            height: 400px;
            margin: 20px 0;
        }
        .info {
            background: #111;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
        }
        .status {
            color: #0f0;
        }
        .error {
            color: #f00;
        }
        .warning {
            color: #ff0;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            background: #333;
            color: #fff;
            border: 1px solid #555;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #555;
        }
        .highlight {
            background: #333;
            padding: 2px 4px;
            border-radius: 2px;
        }
    </style>
</head>
<body>
    <h1>字幕按钮状态修复测试</h1>
    
    <div class="info">
        <div>🎯 <strong>测试目标</strong>：验证字幕按钮状态同步修复</div>
        <div>📝 <strong>问题描述</strong>：字幕已显示但按钮状态显示为"隐藏状态"</div>
        <div>✅ <strong>期望结果</strong>：按钮状态与实际字幕显示状态一致</div>
        <div>🔧 <strong>操作步骤</strong>：</div>
        <div>&nbsp;&nbsp;1. 点击"加载字幕并测试"按钮</div>
        <div>&nbsp;&nbsp;2. 观察字幕按钮的状态是否正确</div>
        <div>&nbsp;&nbsp;3. 点击字幕按钮测试切换功能</div>
    </div>
    
    <div id="Dplayer"></div>
    
    <div>
        <button onclick="loadAndTest()">加载字幕并测试</button>
        <button onclick="checkButtonState()">检查按钮状态</button>
        <button onclick="manualToggle()">手动切换字幕</button>
        <button onclick="clearLogs()">清空日志</button>
    </div>
    
    <div class="info" id="status">等待操作...</div>

    <script src="https://cdn.jsdelivr.net/npm/dplayer@1.27.1/dist/DPlayer.min.js"></script>
    <script>
        function log(message, type = 'status') {
            const statusDiv = document.getElementById('status');
            const time = new Date().toLocaleTimeString();
            statusDiv.innerHTML += `<div class="${type}">[${time}] ${message}</div>`;
            statusDiv.scrollTop = statusDiv.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearLogs() {
            document.getElementById('status').innerHTML = '日志已清空<br>';
        }

        // 测试字幕内容
        const testSubtitle = `WEBVTT

00:00:01.000 --> 00:00:05.000
🎬 字幕按钮状态测试

00:00:06.000 --> 00:00:10.000
✨ 检查按钮状态是否正确同步

00:00:11.000 --> 00:00:15.000
🔄 测试切换功能是否正常

00:00:16.000 --> 00:00:20.000
✅ 修复验证完成`;

        // 初始化DPlayer
        const dp = new DPlayer({
            container: document.getElementById('Dplayer'),
            screenshot: true,
            volume: 0.7,
            video: {
                url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
                pic: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/images/BigBuckBunny.jpg'
            },
            subtitle: {
                url: '',
                type: 'webvtt'
            }
        });

        log('DPlayer 初始化完成');

        // 更新字幕按钮状态 - 使用DPlayer事件系统
        function updateSubtitleButtonState() {
            log('🔄 更新字幕按钮状态');

            // 检查当前字幕状态
            var hasVisibleSubtitles = false;
            if (dp.video.textTracks && dp.video.textTracks.length > 0) {
                for (var i = 0; i < dp.video.textTracks.length; i++) {
                    var track = dp.video.textTracks[i];
                    if (track.mode === 'showing' && track.cues && track.cues.length > 0) {
                        hasVisibleSubtitles = true;
                        break;
                    }
                }
            }

            log(`👁️ 当前字幕可见状态: <span class="highlight">${hasVisibleSubtitles}</span>`);

            // 使用DPlayer的事件系统来更新按钮状态
            if (hasVisibleSubtitles) {
                // 触发字幕显示事件
                dp.events.trigger('subtitle_show');
                log('✅ 触发 subtitle_show 事件');
            } else {
                // 触发字幕隐藏事件
                dp.events.trigger('subtitle_hide');
                log('✅ 触发 subtitle_hide 事件');
            }
        }

        // 设置字幕按钮处理器 - 使用DPlayer事件系统
        function setupSubtitleButtonHandler() {
            log('🎬 设置字幕按钮处理器');

            // 直接更新按钮状态，让DPlayer处理点击事件
            updateSubtitleButtonState();

            var subtitleButton = document.querySelector('.dplayer-subtitle-button');
            if (subtitleButton) {
                log(`✅ 找到字幕按钮: ${subtitleButton.className}`);
                log(`🎯 按钮当前状态: balloon=${subtitleButton.getAttribute('data-balloon')}`);
            } else {
                log('⚠️ 未找到字幕按钮，延迟重试', 'warning');
                setTimeout(setupSubtitleButtonHandler, 1000);
            }
        }

        // 切换字幕显示状态
        function toggleSubtitleDisplay() {
            log('🔄 切换字幕显示状态');
            
            var hasVisibleSubtitles = false;
            var availableTracks = [];
            
            if (dp.video.textTracks && dp.video.textTracks.length > 0) {
                for (var i = 0; i < dp.video.textTracks.length; i++) {
                    var track = dp.video.textTracks[i];
                    if (track.cues && track.cues.length > 0) {
                        availableTracks.push({index: i, label: track.label, mode: track.mode});
                        if (track.mode === 'showing') {
                            hasVisibleSubtitles = true;
                        }
                    }
                }
            }
            
            log(`📊 可用字幕轨道: ${availableTracks.length} 个`);
            log(`👁️ 当前字幕可见状态: <span class="highlight">${hasVisibleSubtitles}</span>`);
            
            if (hasVisibleSubtitles) {
                // 隐藏所有字幕
                for (var i = 0; i < dp.video.textTracks.length; i++) {
                    dp.video.textTracks[i].mode = 'disabled';
                }
                log('🙈 字幕已隐藏');
            } else {
                // 显示字幕
                if (availableTracks.length > 0) {
                    var trackToEnable = availableTracks[0];
                    dp.video.textTracks[trackToEnable.index].mode = 'showing';
                    log(`👁️ 字幕已显示: ${trackToEnable.label}`);
                } else {
                    log('⚠️ 没有可用的字幕轨道', 'warning');
                }
            }
        }

        // 加载字幕并测试
        function loadAndTest() {
            log('🚀 开始加载字幕并测试按钮状态');
            
            // 清理现有字幕
            if (dp.video.textTracks) {
                for (var i = 0; i < dp.video.textTracks.length; i++) {
                    dp.video.textTracks[i].mode = 'disabled';
                }
            }
            var existingTracks = dp.video.querySelectorAll('track');
            existingTracks.forEach(function(track) {
                track.remove();
            });
            
            // 创建新的字幕轨道
            const dataUrl = 'data:text/vtt;charset=utf-8,' + encodeURIComponent(testSubtitle);
            const track = document.createElement('track');
            track.kind = 'subtitles';
            track.src = dataUrl;
            track.srclang = 'zh-CN';
            track.label = '测试字幕';
            track.default = true;
            
            dp.video.appendChild(track);
            
            track.addEventListener('load', function() {
                log('✅ 字幕轨道加载完成');
                track.mode = 'showing';
                
                setTimeout(function() {
                    if (track.cues && track.cues.length > 0) {
                        log(`📝 字幕内容加载成功，cues数量: ${track.cues.length}`);
                        log(`🎯 字幕轨道模式: <span class="highlight">${track.mode}</span>`);
                        
                        // 设置字幕按钮处理器并更新状态
                        setTimeout(function() {
                            setupSubtitleButtonHandler();
                        }, 500);
                    } else {
                        log('⚠️ 字幕轨道无内容', 'warning');
                    }
                }, 500);
            });
            
            // 立即设置为显示模式
            setTimeout(function() {
                track.mode = 'showing';
                log(`🎯 字幕已启用，模式: <span class="highlight">${track.mode}</span>`);
            }, 100);
        }

        function checkButtonState() {
            log('🔍 检查字幕按钮状态');
            
            var subtitleButton = document.querySelector('.dplayer-subtitle-button');
            if (subtitleButton) {
                var hasActiveClass = subtitleButton.classList.contains('dplayer-subtitle-button-active');
                var balloonText = subtitleButton.getAttribute('data-balloon');
                var ariaLabel = subtitleButton.getAttribute('aria-label');
                
                log(`✅ 字幕按钮存在`);
                log(`🎨 按钮激活状态: <span class="highlight">${hasActiveClass}</span>`);
                log(`💬 提示文本: <span class="highlight">${balloonText}</span>`);
                log(`🏷️ 无障碍标签: <span class="highlight">${ariaLabel}</span>`);
                
                // 检查实际字幕状态
                var actuallyVisible = false;
                if (dp.video.textTracks && dp.video.textTracks.length > 0) {
                    for (var i = 0; i < dp.video.textTracks.length; i++) {
                        var track = dp.video.textTracks[i];
                        if (track.mode === 'showing' && track.cues && track.cues.length > 0) {
                            actuallyVisible = true;
                            break;
                        }
                    }
                }
                
                log(`👁️ 实际字幕可见: <span class="highlight">${actuallyVisible}</span>`);
                
                // 检查状态是否一致
                var stateConsistent = (hasActiveClass && actuallyVisible) || (!hasActiveClass && !actuallyVisible);
                if (stateConsistent) {
                    log('✅ 按钮状态与实际字幕状态一致', 'status');
                } else {
                    log('❌ 按钮状态与实际字幕状态不一致！', 'error');
                }
            } else {
                log('❌ 字幕按钮不存在', 'error');
            }
        }

        function manualToggle() {
            log('🔧 手动切换字幕');

            // 检查当前状态并切换
            var hasVisibleSubtitles = false;
            if (dp.video.textTracks && dp.video.textTracks.length > 0) {
                for (var i = 0; i < dp.video.textTracks.length; i++) {
                    var track = dp.video.textTracks[i];
                    if (track.mode === 'showing' && track.cues && track.cues.length > 0) {
                        hasVisibleSubtitles = true;
                        break;
                    }
                }
            }

            if (hasVisibleSubtitles) {
                // 隐藏字幕
                for (var i = 0; i < dp.video.textTracks.length; i++) {
                    dp.video.textTracks[i].mode = 'disabled';
                }
                log('🙈 字幕已隐藏');
                dp.events.trigger('subtitle_hide');
            } else {
                // 显示字幕
                var hasEnabledAny = false;
                for (var i = 0; i < dp.video.textTracks.length; i++) {
                    var track = dp.video.textTracks[i];
                    if (track.cues && track.cues.length > 0) {
                        track.mode = 'showing';
                        hasEnabledAny = true;
                        log(`👁️ 启用字幕轨道: ${track.label}`);
                        break;
                    }
                }

                if (hasEnabledAny) {
                    log('👁️ 字幕已显示');
                    dp.events.trigger('subtitle_show');
                } else {
                    log('⚠️ 没有可用的字幕轨道', 'warning');
                    dp.events.trigger('subtitle_hide');
                }
            }
        }

        // 监听视频事件
        dp.on('loadeddata', function() {
            log('📹 视频数据加载完成');
            setTimeout(function() {
                updateSubtitleButtonState();
            }, 500);
        });

        // 延迟初始化字幕按钮处理器
        setTimeout(function() {
            setupSubtitleButtonHandler();
        }, 2000);
    </script>
</body>
</html>
