<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>字幕按钮测试</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/dplayer@1.27.1/dist/DPlayer.min.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #000;
            color: #fff;
            font-family: Arial, sans-serif;
        }
        #Dplayer {
            width: 100%;
            height: 500px;
            margin: 20px 0;
        }
        .info {
            background: #111;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 14px;
        }
        .status {
            color: #0f0;
        }
        .error {
            color: #f00;
        }
        .warning {
            color: #ff0;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            background: #333;
            color: #fff;
            border: 1px solid #555;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #555;
        }
    </style>
</head>
<body>
    <h1>DPlayer 字幕按钮测试</h1>
    
    <div class="info">
        <div>📝 测试步骤：</div>
        <div>1. 点击"加载测试字幕"按钮</div>
        <div>2. 观察DPlayer底部控制栏是否出现字幕按钮（CC图标）</div>
        <div>3. 点击字幕按钮，观察字幕是否正确显示/隐藏</div>
        <div>4. 查看控制台日志了解详细状态</div>
    </div>
    
    <div id="Dplayer"></div>
    
    <div>
        <button onclick="loadTestSubtitle()">加载测试字幕</button>
        <button onclick="checkSubtitleButton()">检查字幕按钮</button>
        <button onclick="manualToggle()">手动切换字幕</button>
    </div>
    
    <div class="info" id="status">等待操作...</div>

    <script src="https://cdn.jsdelivr.net/npm/dplayer@1.27.1/dist/DPlayer.min.js"></script>
    <script>
        function updateStatus(message, type = 'status') {
            const statusDiv = document.getElementById('status');
            const time = new Date().toLocaleTimeString();
            statusDiv.innerHTML += `<div class="${type}">[${time}] ${message}</div>`;
            statusDiv.scrollTop = statusDiv.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        // 创建测试字幕内容
        const testSubtitle = `WEBVTT

00:00:01.000 --> 00:00:05.000
🎬 这是第一条测试字幕

00:00:06.000 --> 00:00:10.000
✨ 字幕按钮功能测试中

00:00:11.000 --> 00:00:15.000
🔄 请点击底部的字幕按钮测试切换功能

00:00:16.000 --> 00:00:20.000
✅ 如果能看到这条字幕说明功能正常`;

        // 初始化DPlayer - 确保显示字幕按钮
        const dp = new DPlayer({
            container: document.getElementById('Dplayer'),
            screenshot: true,
            volume: 0.7,
            video: {
                url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
                pic: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/images/BigBuckBunny.jpg'
            },
            subtitle: {
                url: '',
                type: 'webvtt'
            }
        });

        updateStatus('DPlayer 初始化完成');

        // 设置字幕按钮处理器
        setTimeout(function() {
            setupSubtitleButtonHandler();
        }, 2000);

        function loadTestSubtitle() {
            updateStatus('开始加载测试字幕');
            
            // 保存当前播放状态
            const wasPlaying = !dp.video.paused;
            const currentTime = dp.video.currentTime;
            
            // 清理现有字幕
            clearExistingSubtitles();
            
            // 创建data URL
            const dataUrl = 'data:text/vtt;charset=utf-8,' + encodeURIComponent(testSubtitle);
            
            // 创建新的字幕轨道
            const track = document.createElement('track');
            track.kind = 'subtitles';
            track.src = dataUrl;
            track.srclang = 'zh-CN';
            track.label = '测试字幕';
            track.default = true;
            
            // 添加到video元素
            dp.video.appendChild(track);
            
            // 等待轨道加载完成后启用
            track.addEventListener('load', function() {
                updateStatus('✅ 字幕轨道加载完成');
                track.mode = 'showing';
                
                setTimeout(function() {
                    if (track.cues && track.cues.length > 0) {
                        updateStatus(`📝 字幕加载成功，cues数量: ${track.cues.length}`);
                        updateStatus(`🎯 字幕轨道模式: ${track.mode}`);
                        
                        // 恢复播放状态
                        if (wasPlaying) {
                            dp.video.currentTime = currentTime;
                            dp.play();
                        }
                        
                        // 重新设置字幕按钮处理器
                        setTimeout(function() {
                            setupSubtitleButtonHandler();
                        }, 500);
                    } else {
                        updateStatus('⚠️ 字幕轨道无内容', 'warning');
                    }
                }, 500);
            });
            
            track.addEventListener('error', function(e) {
                updateStatus(`❌ 字幕轨道加载失败: ${e.message}`, 'error');
            });
            
            // 立即设置为显示模式
            setTimeout(function() {
                track.mode = 'showing';
                updateStatus(`🎯 字幕已启用，模式: ${track.mode}`);
            }, 100);
        }

        function clearExistingSubtitles() {
            try {
                // 禁用所有现有字幕轨道
                if (dp.video.textTracks) {
                    for (let i = 0; i < dp.video.textTracks.length; i++) {
                        dp.video.textTracks[i].mode = 'disabled';
                    }
                }

                // 移除现有的track元素
                const existingTracks = dp.video.querySelectorAll('track');
                existingTracks.forEach(function(track) {
                    track.remove();
                });
                
                updateStatus('🧹 已清理现有字幕轨道');
            } catch (error) {
                updateStatus(`❌ 清理字幕轨道时出错: ${error.message}`, 'error');
            }
        }

        function setupSubtitleButtonHandler() {
            updateStatus('🎬 设置字幕按钮处理器');
            
            // 查找字幕按钮 - 使用正确的选择器
            var subtitleButton = document.querySelector('.dplayer-subtitle-button');
            if (!subtitleButton) {
                // 如果没有找到，尝试其他可能的选择器
                subtitleButton = document.querySelector('.dplayer-subtitle-btn');
                if (!subtitleButton) {
                    subtitleButton = document.querySelector('[data-balloon*="字幕"]');
                    if (!subtitleButton) {
                        subtitleButton = document.querySelector('[data-balloon*="show-subs"]');
                        if (!subtitleButton) {
                            subtitleButton = document.querySelector('[data-balloon*="hide-subs"]');
                        }
                    }
                }
            }
            
            if (subtitleButton) {
                updateStatus(`✅ 找到字幕按钮: ${subtitleButton.className}`);
                
                // 移除原有的事件监听器（如果有的话）
                var newButton = subtitleButton.cloneNode(true);
                subtitleButton.parentNode.replaceChild(newButton, subtitleButton);
                
                // 首先同步按钮状态
                updateSubtitleButtonState(subtitleButton);

                // 添加新的点击事件监听器
                newButton.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    updateStatus('🎯 字幕按钮被点击');
                    toggleSubtitleDisplay();

                    // 更新按钮状态
                    setTimeout(function() {
                        updateSubtitleButtonState(newButton);
                    }, 100);
                });

                updateStatus('✅ 字幕按钮事件监听器已设置');

                // 保存按钮引用以便后续更新状态
                dp._customSubtitleButton = newButton;
            } else {
                updateStatus('⚠️ 未找到字幕按钮，尝试查找所有可能的按钮', 'warning');
                
                // 打印所有可能的按钮供调试
                var allButtons = document.querySelectorAll('.dplayer-icons *');
                updateStatus(`🔍 所有DPlayer图标按钮数量: ${allButtons.length}`);
                
                // 尝试通过文本内容查找
                Array.from(allButtons).forEach(function(btn, index) {
                    updateStatus(`按钮 ${index}: ${btn.className} | ${btn.textContent} | ${btn.getAttribute('data-balloon')}`);
                });
                
                // 延迟重试
                setTimeout(function() {
                    setupSubtitleButtonHandler();
                }, 2000);
            }
        }

        // 更新字幕按钮状态
        function updateSubtitleButtonState(button) {
            if (!button) {
                button = dp._customSubtitleButton || document.querySelector('.dplayer-subtitle-button');
            }

            if (!button) {
                updateStatus('⚠️ 无法找到字幕按钮来更新状态', 'warning');
                return;
            }

            // 检查当前字幕状态
            var hasVisibleSubtitles = false;
            if (dp.video.textTracks && dp.video.textTracks.length > 0) {
                for (var i = 0; i < dp.video.textTracks.length; i++) {
                    var track = dp.video.textTracks[i];
                    if (track.mode === 'showing' && track.cues && track.cues.length > 0) {
                        hasVisibleSubtitles = true;
                        break;
                    }
                }
            }

            updateStatus(`🔄 更新字幕按钮状态，当前字幕可见: ${hasVisibleSubtitles}`);

            // 更新按钮的视觉状态和提示文本
            if (hasVisibleSubtitles) {
                button.classList.add('dplayer-subtitle-button-active');
                button.setAttribute('data-balloon', 'hide-subs');
                button.setAttribute('aria-label', '隐藏字幕');
            } else {
                button.classList.remove('dplayer-subtitle-button-active');
                button.setAttribute('data-balloon', 'show-subs');
                button.setAttribute('aria-label', '显示字幕');
            }
        }

        function toggleSubtitleDisplay() {
            updateStatus('🔄 切换字幕显示状态');
            
            // 检查当前字幕状态
            var hasVisibleSubtitles = false;
            var subtitleTracks = [];
            
            if (dp.video.textTracks && dp.video.textTracks.length > 0) {
                for (var i = 0; i < dp.video.textTracks.length; i++) {
                    var track = dp.video.textTracks[i];
                    subtitleTracks.push({
                        index: i,
                        label: track.label,
                        mode: track.mode,
                        cues: track.cues ? track.cues.length : 0
                    });
                    
                    if (track.mode === 'showing') {
                        hasVisibleSubtitles = true;
                    }
                }
            }
            
            updateStatus(`📊 当前字幕轨道状态: ${JSON.stringify(subtitleTracks)}`);
            updateStatus(`👁️ 当前字幕可见状态: ${hasVisibleSubtitles}`);
            
            // 切换字幕显示状态
            if (hasVisibleSubtitles) {
                // 隐藏所有字幕
                for (var i = 0; i < dp.video.textTracks.length; i++) {
                    dp.video.textTracks[i].mode = 'disabled';
                }
                updateStatus('🙈 字幕已隐藏');
            } else {
                // 显示字幕
                var hasEnabledAny = false;
                for (var i = 0; i < dp.video.textTracks.length; i++) {
                    var track = dp.video.textTracks[i];
                    if (track.cues && track.cues.length > 0) {
                        track.mode = 'showing';
                        hasEnabledAny = true;
                        updateStatus(`👁️ 启用字幕轨道: ${track.label}`);
                        break; // 只启用第一个有内容的轨道
                    }
                }
                
                if (hasEnabledAny) {
                    updateStatus('👁️ 字幕已显示');
                } else {
                    updateStatus('⚠️ 没有可用的字幕轨道', 'warning');
                }
            }
            
            // 打印切换后的状态
            setTimeout(function() {
                var newSubtitleTracks = [];
                for (var i = 0; i < dp.video.textTracks.length; i++) {
                    var track = dp.video.textTracks[i];
                    newSubtitleTracks.push({
                        index: i,
                        label: track.label,
                        mode: track.mode,
                        cues: track.cues ? track.cues.length : 0
                    });
                }
                updateStatus(`📊 切换后字幕轨道状态: ${JSON.stringify(newSubtitleTracks)}`);
            }, 100);
        }

        function checkSubtitleButton() {
            updateStatus('🔍 检查字幕按钮状态');
            
            var subtitleButton = document.querySelector('.dplayer-subtitle-button');
            if (subtitleButton) {
                updateStatus(`✅ 字幕按钮存在: ${subtitleButton.className}`);
                updateStatus(`📍 按钮位置: ${subtitleButton.getBoundingClientRect().x}, ${subtitleButton.getBoundingClientRect().y}`);
                updateStatus(`👁️ 按钮可见: ${subtitleButton.offsetWidth > 0 && subtitleButton.offsetHeight > 0}`);
            } else {
                updateStatus('❌ 字幕按钮不存在', 'error');
            }
            
            // 检查所有控制按钮
            var allIcons = document.querySelectorAll('.dplayer-icons > *');
            updateStatus(`🎛️ 控制栏按钮总数: ${allIcons.length}`);
            Array.from(allIcons).forEach(function(icon, index) {
                updateStatus(`按钮 ${index}: ${icon.className}`);
            });
        }

        function manualToggle() {
            updateStatus('🔧 手动切换字幕');
            toggleSubtitleDisplay();
        }

        // 监听视频事件
        dp.on('loadeddata', function() {
            updateStatus('📹 视频加载完成，更新字幕按钮状态');
            setTimeout(function() {
                updateSubtitleButtonState();
            }, 500);
        });

        dp.on('play', function() {
            updateStatus('▶️ 视频开始播放');
        });

        dp.on('pause', function() {
            updateStatus('⏸️ 视频暂停');
        });
    </script>
</body>
</html>
