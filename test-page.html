<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>115网盘字幕修复测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-button {
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
        }
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,123,255,0.3);
        }
        .test-button:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }
        .console-output {
            background: #1e1e1e;
            color: #00ff00;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
            margin: 20px 0;
            border: 2px solid #333;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background: #28a745; }
        .status-error { background: #dc3545; }
        .status-info { background: #17a2b8; }
        .button-container {
            text-align: center;
            margin: 20px 0;
        }
        .progress-bar {
            width: 100%;
            height: 6px;
            background: #e9ecef;
            border-radius: 3px;
            overflow: hidden;
            margin: 20px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #007bff, #28a745);
            width: 0%;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎬 115网盘字幕修复测试</h1>
        
        <div class="button-container">
            <button class="test-button" onclick="runTests()" id="testBtn">
                🚀 开始测试
            </button>
            <button class="test-button" onclick="clearConsole()">
                🗑️ 清空日志
            </button>
        </div>
        
        <div class="progress-bar">
            <div class="progress-fill" id="progressBar"></div>
        </div>
        
        <div class="console-output" id="console">
            <div>📋 控制台输出将显示在这里...</div>
            <div>💡 点击"开始测试"按钮来测试字幕加载方法</div>
        </div>
    </div>

    <script>
        // 重写console.log来显示在页面上
        const originalConsoleLog = console.log;
        const consoleElement = document.getElementById('console');
        
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            
            const message = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');
            
            const div = document.createElement('div');
            div.style.marginBottom = '5px';
            div.innerHTML = message;
            
            consoleElement.appendChild(div);
            consoleElement.scrollTop = consoleElement.scrollHeight;
        };
        
        function clearConsole() {
            consoleElement.innerHTML = '<div>📋 控制台已清空</div>';
        }
        
        function updateProgress(percent) {
            document.getElementById('progressBar').style.width = percent + '%';
        }
        
        async function runTests() {
            const testBtn = document.getElementById('testBtn');
            testBtn.disabled = true;
            testBtn.textContent = '🔄 测试中...';
            
            clearConsole();
            updateProgress(0);
            
            console.log('=== 115网盘字幕修复测试开始 ===');
            console.log('');
            
            // 测试用的字幕URL
            const TEST_SUBTITLE_URL = 'https://aps.115.com/transcode/1751781935/31267f5../CE9B0userscript.html?name=c-c789f98bce33:16405706C2584A658F0ECE2DB4E56217B76962E?type=.srt';
            
            const results = [];
            
            // 测试1: 简单fetch
            console.log('🧪 测试1: 简单fetch方法');
            updateProgress(10);
            
            try {
                console.log('📡 发送请求:', TEST_SUBTITLE_URL);
                
                const response = await fetch(TEST_SUBTITLE_URL, {
                    method: 'GET',
                    mode: 'cors'
                });
                
                console.log('📊 响应状态:', response.status, response.statusText);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const text = await response.text();
                console.log('✅ 简单fetch成功！字幕长度:', text.length);
                console.log('📄 内容预览:', text.substring(0, 100) + '...');
                
                results.push({ success: true, method: 'simple-fetch' });
            } catch (error) {
                console.log('❌ 简单fetch失败:', error.message);
                results.push({ success: false, method: 'simple-fetch', error: error.message });
            }
            
            updateProgress(40);
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 测试2: CORS代理
            console.log('');
            console.log('🧪 测试2: CORS代理方法');
            
            const proxyUrls = [
                `https://api.allorigins.win/get?url=${encodeURIComponent(TEST_SUBTITLE_URL)}`,
                `https://corsproxy.io/?${encodeURIComponent(TEST_SUBTITLE_URL)}`
            ];
            
            let proxySuccess = false;
            
            for (let i = 0; i < proxyUrls.length; i++) {
                try {
                    const proxyUrl = proxyUrls[i];
                    console.log(`📡 尝试代理 ${i + 1}:`, proxyUrl.substring(0, 80) + '...');
                    
                    const response = await fetch(proxyUrl);
                    console.log('📊 代理响应状态:', response.status, response.statusText);
                    
                    if (!response.ok) {
                        throw new Error(`代理响应失败: ${response.status}`);
                    }
                    
                    const data = await response.json();
                    const subtitleText = data.contents || data.data || data;
                    
                    if (typeof subtitleText !== 'string') {
                        throw new Error('代理返回的数据格式不正确');
                    }
                    
                    console.log(`✅ 代理方法 ${i + 1} 成功！字幕长度:`, subtitleText.length);
                    console.log('📄 内容预览:', subtitleText.substring(0, 100) + '...');
                    
                    results.push({ success: true, method: `cors-proxy-${i + 1}` });
                    proxySuccess = true;
                    break;
                } catch (error) {
                    console.log(`❌ 代理 ${i + 1} 失败:`, error.message);
                }
            }
            
            if (!proxySuccess) {
                results.push({ success: false, method: 'cors-proxy', error: '所有代理都失败了' });
            }
            
            updateProgress(70);
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 测试3: 网络连通性
            console.log('');
            console.log('🧪 测试3: 网络连通性检查');
            
            try {
                console.log('🌐 检查115网盘主站连通性...');
                await fetch('https://115.com', { 
                    method: 'HEAD',
                    mode: 'no-cors'
                });
                console.log('✅ 115网盘主站连通正常');
                
                console.log('🌐 检查字幕服务器连通性...');
                await fetch('https://aps.115.com', { 
                    method: 'HEAD',
                    mode: 'no-cors'
                });
                console.log('✅ 字幕服务器连通正常');
                
                results.push({ success: true, method: 'connectivity' });
            } catch (error) {
                console.log('❌ 网络连通性检查失败:', error.message);
                results.push({ success: false, method: 'connectivity', error: error.message });
            }
            
            updateProgress(100);
            
            // 输出测试结果总结
            console.log('');
            console.log('📊 测试结果总结:');
            console.log('==================');
            
            results.forEach((result, index) => {
                const status = result.success ? '✅ 成功' : '❌ 失败';
                console.log(`测试 ${index + 1} (${result.method}): ${status}`);
                if (!result.success && result.error) {
                    console.log(`   错误: ${result.error}`);
                }
            });
            
            // 提供建议
            console.log('');
            console.log('💡 修复建议:');
            const successfulMethods = results.filter(r => r.success);
            
            if (successfulMethods.length === 0) {
                console.log('❌ 所有方法都失败了，建议：');
                console.log('   1. 确保在115网盘视频播放页面运行脚本');
                console.log('   2. 使用Tampermonkey等用户脚本管理器');
                console.log('   3. 检查浏览器是否阻止了跨域请求');
                console.log('   4. 尝试关闭浏览器的严格安全设置');
            } else {
                console.log('✅ 以下方法可用，脚本已相应优化：');
                successfulMethods.forEach(method => {
                    console.log(`   - ${method.method}`);
                });
                console.log('');
                console.log('🎉 字幕功能应该可以正常工作了！');
            }
            
            console.log('');
            console.log('=== 测试完成 ===');
            
            testBtn.disabled = false;
            testBtn.textContent = '🚀 重新测试';
        }
    </script>
</body>
</html>
