{"cells": [{"cell_type": "code", "execution_count": 1, "id": "707f5f10", "metadata": {}, "outputs": [], "source": ["import pandas as pd"]}, {"cell_type": "code", "execution_count": 2, "id": "16d19a72", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>查询条件</th>\n", "      <th>查询值</th>\n", "      <th>序号</th>\n", "      <th>发票类型</th>\n", "      <th>销售渠道</th>\n", "      <th>RTM</th>\n", "      <th>项目名称</th>\n", "      <th>发票代码</th>\n", "      <th>发票号码</th>\n", "      <th>全电发票号码</th>\n", "      <th>...</th>\n", "      <th>备注</th>\n", "      <th>异常原因</th>\n", "      <th>校验不通过原因</th>\n", "      <th>MPN是否校验通过</th>\n", "      <th>校验是否通过</th>\n", "      <th>财务确认状态</th>\n", "      <th>财务确认数量</th>\n", "      <th>财务确认日期</th>\n", "      <th>财务确认季度</th>\n", "      <th>财务批复说明</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>查询出的记录数</td>\n", "      <td>1188344</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>原始文件</td>\n", "      <td>2025-07-03发票信息综合查询导出.xlsx</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>1.0</td>\n", "      <td>电子发票(增值税专用发票)</td>\n", "      <td>EC</td>\n", "      <td>ENT</td>\n", "      <td>合规</td>\n", "      <td>2.533200e+11</td>\n", "      <td>74171956.0</td>\n", "      <td>25332000000074171956</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>通过</td>\n", "      <td>通过</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>2.0</td>\n", "      <td>电子发票(增值税专用发票)</td>\n", "      <td>EC</td>\n", "      <td>ENT</td>\n", "      <td>合规</td>\n", "      <td>2.533200e+11</td>\n", "      <td>74191736.0</td>\n", "      <td>25332000000074191736</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>通过</td>\n", "      <td>通过</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>3.0</td>\n", "      <td>电子发票(增值税专用发票)</td>\n", "      <td>EC</td>\n", "      <td>ENT</td>\n", "      <td>合规</td>\n", "      <td>2.533200e+11</td>\n", "      <td>74270468.0</td>\n", "      <td>25332000000074270468</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>通过</td>\n", "      <td>通过</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1188341</th>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>288340.0</td>\n", "      <td>增值税专用发票</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>SEI</td>\n", "      <td>1.100233e+09</td>\n", "      <td>33834530.0</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>通过</td>\n", "      <td>通过</td>\n", "      <td>全部付款</td>\n", "      <td>15.0</td>\n", "      <td>2024-09-12</td>\n", "      <td>FY24Q4</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1188342</th>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>288341.0</td>\n", "      <td>增值税专用发票</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>SEI</td>\n", "      <td>1.100233e+09</td>\n", "      <td>33834530.0</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>通过</td>\n", "      <td>通过</td>\n", "      <td>全部付款</td>\n", "      <td>16.0</td>\n", "      <td>2024-09-12</td>\n", "      <td>FY24Q4</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1188343</th>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>288342.0</td>\n", "      <td>增值税专用发票</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>SEI</td>\n", "      <td>1.100233e+09</td>\n", "      <td>33834529.0</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>通过</td>\n", "      <td>通过</td>\n", "      <td>全部付款</td>\n", "      <td>10.0</td>\n", "      <td>2024-09-12</td>\n", "      <td>FY24Q4</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1188344</th>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>288343.0</td>\n", "      <td>增值税专用发票</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>SEI</td>\n", "      <td>1.100233e+09</td>\n", "      <td>33834529.0</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>通过</td>\n", "      <td>通过</td>\n", "      <td>全部付款</td>\n", "      <td>218.0</td>\n", "      <td>2024-09-12</td>\n", "      <td>FY24Q4</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1188345</th>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>288344.0</td>\n", "      <td>增值税专用发票</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>SEI</td>\n", "      <td>1.100233e+09</td>\n", "      <td>33834529.0</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>通过</td>\n", "      <td>通过</td>\n", "      <td>全部付款</td>\n", "      <td>7.0</td>\n", "      <td>2024-09-12</td>\n", "      <td>FY24Q4</td>\n", "      <td>None</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1188346 rows × 52 columns</p>\n", "</div>"], "text/plain": ["            查询条件                        查询值        序号           发票类型  销售渠道  \\\n", "0        查询出的记录数                    1188344       NaN           None  None   \n", "1           原始文件  2025-07-03发票信息综合查询导出.xlsx       NaN           None  None   \n", "2           None                       None       1.0  电子发票(增值税专用发票)    EC   \n", "3           None                       None       2.0  电子发票(增值税专用发票)    EC   \n", "4           None                       None       3.0  电子发票(增值税专用发票)    EC   \n", "...          ...                        ...       ...            ...   ...   \n", "1188341     None                       None  288340.0        增值税专用发票  None   \n", "1188342     None                       None  288341.0        增值税专用发票  None   \n", "1188343     None                       None  288342.0        增值税专用发票  None   \n", "1188344     None                       None  288343.0        增值税专用发票  None   \n", "1188345     None                       None  288344.0        增值税专用发票  None   \n", "\n", "          RTM  项目名称          发票代码        发票号码                全电发票号码  ...  \\\n", "0        None  None           NaN         NaN                  None  ...   \n", "1        None  None           NaN         NaN                  None  ...   \n", "2         ENT    合规  2.533200e+11  74171956.0  25332000000074171956  ...   \n", "3         ENT    合规  2.533200e+11  74191736.0  25332000000074191736  ...   \n", "4         ENT    合规  2.533200e+11  74270468.0  25332000000074270468  ...   \n", "...       ...   ...           ...         ...                   ...  ...   \n", "1188341  None   SEI  1.100233e+09  33834530.0                  None  ...   \n", "1188342  None   SEI  1.100233e+09  33834530.0                  None  ...   \n", "1188343  None   SEI  1.100233e+09  33834529.0                  None  ...   \n", "1188344  None   SEI  1.100233e+09  33834529.0                  None  ...   \n", "1188345  None   SEI  1.100233e+09  33834529.0                  None  ...   \n", "\n", "           备注 异常原因 校验不通过原因 MPN是否校验通过 校验是否通过 财务确认状态 财务确认数量      财务确认日期  财务确认季度  \\\n", "0        None  NaN    None      None   None   None    NaN        None    None   \n", "1        None  NaN    None      None   None   None    NaN        None    None   \n", "2        None  NaN    None        通过     通过   None    NaN        None    None   \n", "3        None  NaN    None        通过     通过   None    NaN        None    None   \n", "4        None  NaN    None        通过     通过   None    NaN        None    None   \n", "...       ...  ...     ...       ...    ...    ...    ...         ...     ...   \n", "1188341  None  NaN    None        通过     通过   全部付款   15.0  2024-09-12  FY24Q4   \n", "1188342  None  NaN    None        通过     通过   全部付款   16.0  2024-09-12  FY24Q4   \n", "1188343  None  NaN    None        通过     通过   全部付款   10.0  2024-09-12  FY24Q4   \n", "1188344  None  NaN    None        通过     通过   全部付款  218.0  2024-09-12  FY24Q4   \n", "1188345  None  NaN    None        通过     通过   全部付款    7.0  2024-09-12  FY24Q4   \n", "\n", "        财务批复说明  \n", "0         None  \n", "1         None  \n", "2         None  \n", "3         None  \n", "4         None  \n", "...        ...  \n", "1188341   None  \n", "1188342   None  \n", "1188343   None  \n", "1188344   None  \n", "1188345   None  \n", "\n", "[1188346 rows x 52 columns]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["dfvat = pd.read_parquet('/Users/<USER>/Downloads/2025-07-03发票信息综合查询.parquet')\n", "dfvat"]}, {"cell_type": "code", "execution_count": 4, "id": "667825e2", "metadata": {}, "outputs": [{"data": {"text/plain": ["['查询条件',\n", " '查询值',\n", " '序号',\n", " '发票类型',\n", " '销售渠道',\n", " 'RTM',\n", " '项目名称',\n", " '发票代码',\n", " '发票号码',\n", " '全电发票号码',\n", " '发票行号',\n", " '发票日期',\n", " '发票所属季度',\n", " '发票所属季度周',\n", " '收票日期',\n", " '发票恢复时间',\n", " '经销商总公司HQ ID',\n", " '经销商总公司Sold to ID',\n", " '经销商总公司名称',\n", " '总代名称',\n", " '总代HQ ID',\n", " '总代Sold to ID',\n", " '销方名称',\n", " '销方税号',\n", " 'ENT总经销商名称',\n", " 'ENT总经销商税号',\n", " '销方HQ ID',\n", " '销方Sold to ID',\n", " '企业名称',\n", " '企业税号',\n", " '购方名称',\n", " '购方税号',\n", " '商品名称（销售产品）',\n", " '票面MPN',\n", " 'MPN',\n", " '项目分类',\n", " '数量',\n", " '原蓝票全电发票号码',\n", " '原蓝票发票代码',\n", " '原蓝票发票号码',\n", " '蓝票所属季度',\n", " '发票状态',\n", " '备注',\n", " '异常原因',\n", " '校验不通过原因',\n", " 'MPN是否校验通过',\n", " '校验是否通过',\n", " '财务确认状态',\n", " '财务确认数量',\n", " '财务确认日期',\n", " '财务确认季度',\n", " '财务批复说明']"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["list(dfvat)"]}, {"cell_type": "code", "execution_count": 7, "id": "3b85c3cb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["发票日期最小值： 2023-07-03 00:00:00\n", "收票日期最小值： 2023-08-23 16:57:05\n"]}], "source": ["dfvat['发票日期'] = pd.to_datetime(dfvat['发票日期'])\n", "dfvat['收票日期'] = pd.to_datetime(dfvat['收票日期'])\n", "\n", "print(\"发票日期最小值：\", dfvat['发票日期'].min())\n", "print(\"收票日期最小值：\", dfvat['收票日期'].min())"]}, {"cell_type": "code", "execution_count": 8, "id": "152bfba2", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/var/folders/c3/g19n4q1x2jq2g5pqqrp6pnn00000gp/T/ipykernel_59546/1876943713.py:1: DtypeWarning: Columns (2,3,13,18,19,24,26,27,35,37,38,42,45,47,48,49) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  dfvat_api = pd.read_csv('/Users/<USER>/Library/CloudStorage/Box-Box/Planning Team/Tableau Auto-Refresh Raw Data/99 Hierarchy/SEI/input/发票查询明细_api.csv')\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>序号</th>\n", "      <th>发票类型</th>\n", "      <th>销售渠道</th>\n", "      <th>RTM</th>\n", "      <th>项目名称</th>\n", "      <th>发票代码</th>\n", "      <th>发票号码</th>\n", "      <th>全电发票号码</th>\n", "      <th>发票行号</th>\n", "      <th>发票日期</th>\n", "      <th>...</th>\n", "      <th>异常原因</th>\n", "      <th>校验不通过原因</th>\n", "      <th>MPN是否校验通过</th>\n", "      <th>校验是否通过</th>\n", "      <th>财务确认状态</th>\n", "      <th>财务确认数量</th>\n", "      <th>财务确认日期</th>\n", "      <th>财务确认季度</th>\n", "      <th>财务批复说明</th>\n", "      <th>update_time</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>NaN</td>\n", "      <td>电子发票(增值税专用发票)</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>SEI</td>\n", "      <td>251220000000</td>\n", "      <td>19832045</td>\n", "      <td>25122000000019832045</td>\n", "      <td>1</td>\n", "      <td>2025-03-28</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>通过</td>\n", "      <td>通过</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2025-07-07 09:34:22</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>NaN</td>\n", "      <td>电子发票(增值税专用发票)</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>SEI</td>\n", "      <td>251220000000</td>\n", "      <td>19833727</td>\n", "      <td>25122000000019833727</td>\n", "      <td>1</td>\n", "      <td>2025-03-28</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>通过</td>\n", "      <td>通过</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2025-07-07 09:34:22</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>NaN</td>\n", "      <td>电子发票(增值税专用发票)</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>SEI</td>\n", "      <td>251220000000</td>\n", "      <td>19833727</td>\n", "      <td>25122000000019833727</td>\n", "      <td>2</td>\n", "      <td>2025-03-28</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>通过</td>\n", "      <td>通过</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2025-07-07 09:34:22</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>NaN</td>\n", "      <td>电子发票(增值税专用发票)</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>SEI</td>\n", "      <td>251220000000</td>\n", "      <td>19328472</td>\n", "      <td>25122000000019328472</td>\n", "      <td>1</td>\n", "      <td>2025-03-27</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>通过</td>\n", "      <td>通过</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2025-07-07 09:34:22</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>NaN</td>\n", "      <td>电子发票(增值税专用发票)</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>SEI</td>\n", "      <td>251220000000</td>\n", "      <td>19328472</td>\n", "      <td>25122000000019328472</td>\n", "      <td>2</td>\n", "      <td>2025-03-27</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>通过</td>\n", "      <td>通过</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2025-07-07 09:34:22</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1206584</th>\n", "      <td>NaN</td>\n", "      <td>电子发票(增值税专用发票)</td>\n", "      <td>NaN</td>\n", "      <td>ENT</td>\n", "      <td>合规</td>\n", "      <td>259520000001</td>\n", "      <td>21676850</td>\n", "      <td>25952000000121676850</td>\n", "      <td>1</td>\n", "      <td>2025-06-18</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>通过</td>\n", "      <td>通过</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2025-07-07 09:34:22</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1206585</th>\n", "      <td>NaN</td>\n", "      <td>电子发票(增值税专用发票)</td>\n", "      <td>NaN</td>\n", "      <td>ENT</td>\n", "      <td>合规</td>\n", "      <td>259520000001</td>\n", "      <td>21666786</td>\n", "      <td>25952000000121666786</td>\n", "      <td>1</td>\n", "      <td>2025-06-18</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>通过</td>\n", "      <td>通过</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2025-07-07 09:34:22</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1206586</th>\n", "      <td>NaN</td>\n", "      <td>电子发票(增值税普通发票)</td>\n", "      <td>NaN</td>\n", "      <td>ENT</td>\n", "      <td>合规</td>\n", "      <td>259520000001</td>\n", "      <td>21713452</td>\n", "      <td>25952000000121713452</td>\n", "      <td>1</td>\n", "      <td>2025-06-18</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>通过</td>\n", "      <td>通过</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2025-07-07 09:34:22</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1206587</th>\n", "      <td>NaN</td>\n", "      <td>电子发票(增值税专用发票)</td>\n", "      <td>NaN</td>\n", "      <td>ENT</td>\n", "      <td>合规</td>\n", "      <td>259520000001</td>\n", "      <td>21713451</td>\n", "      <td>25952000000121713451</td>\n", "      <td>1</td>\n", "      <td>2025-06-18</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>通过</td>\n", "      <td>通过</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2025-07-07 09:34:22</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1206588</th>\n", "      <td>NaN</td>\n", "      <td>电子发票(增值税专用发票)</td>\n", "      <td>NaN</td>\n", "      <td>ENT</td>\n", "      <td>合规</td>\n", "      <td>259520000001</td>\n", "      <td>21666781</td>\n", "      <td>25952000000121666781</td>\n", "      <td>1</td>\n", "      <td>2025-06-18</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>通过</td>\n", "      <td>通过</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2025-07-07 09:34:22</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1206589 rows × 51 columns</p>\n", "</div>"], "text/plain": ["         序号           发票类型 销售渠道  RTM 项目名称          发票代码      发票号码  \\\n", "0       NaN  电子发票(增值税专用发票)  NaN  NaN  SEI  251220000000  19832045   \n", "1       NaN  电子发票(增值税专用发票)  NaN  NaN  SEI  251220000000  19833727   \n", "2       NaN  电子发票(增值税专用发票)  NaN  NaN  SEI  251220000000  19833727   \n", "3       NaN  电子发票(增值税专用发票)  NaN  NaN  SEI  251220000000  19328472   \n", "4       NaN  电子发票(增值税专用发票)  NaN  NaN  SEI  251220000000  19328472   \n", "...      ..            ...  ...  ...  ...           ...       ...   \n", "1206584 NaN  电子发票(增值税专用发票)  NaN  ENT   合规  259520000001  21676850   \n", "1206585 NaN  电子发票(增值税专用发票)  NaN  ENT   合规  259520000001  21666786   \n", "1206586 NaN  电子发票(增值税普通发票)  NaN  ENT   合规  259520000001  21713452   \n", "1206587 NaN  电子发票(增值税专用发票)  NaN  ENT   合规  259520000001  21713451   \n", "1206588 NaN  电子发票(增值税专用发票)  NaN  ENT   合规  259520000001  21666781   \n", "\n", "                       全电发票号码  发票行号        发票日期  ... 异常原因 校验不通过原因 MPN是否校验通过  \\\n", "0        25122000000019832045     1  2025-03-28  ...  NaN     NaN        通过   \n", "1        25122000000019833727     1  2025-03-28  ...  NaN     NaN        通过   \n", "2        25122000000019833727     2  2025-03-28  ...  NaN     NaN        通过   \n", "3        25122000000019328472     1  2025-03-27  ...  NaN     NaN        通过   \n", "4        25122000000019328472     2  2025-03-27  ...  NaN     NaN        通过   \n", "...                       ...   ...         ...  ...  ...     ...       ...   \n", "1206584  25952000000121676850     1  2025-06-18  ...  NaN     NaN        通过   \n", "1206585  25952000000121666786     1  2025-06-18  ...  NaN     NaN        通过   \n", "1206586  25952000000121713452     1  2025-06-18  ...  NaN     NaN        通过   \n", "1206587  25952000000121713451     1  2025-06-18  ...  NaN     NaN        通过   \n", "1206588  25952000000121666781     1  2025-06-18  ...  NaN     NaN        通过   \n", "\n", "        校验是否通过 财务确认状态 财务确认数量 财务确认日期 财务确认季度 财务批复说明          update_time  \n", "0           通过    NaN      0    NaN    NaN    NaN  2025-07-07 09:34:22  \n", "1           通过    NaN      0    NaN    NaN    NaN  2025-07-07 09:34:22  \n", "2           通过    NaN      0    NaN    NaN    NaN  2025-07-07 09:34:22  \n", "3           通过    NaN      0    NaN    NaN    NaN  2025-07-07 09:34:22  \n", "4           通过    NaN      0    NaN    NaN    NaN  2025-07-07 09:34:22  \n", "...        ...    ...    ...    ...    ...    ...                  ...  \n", "1206584     通过    NaN      0    NaN    NaN    NaN  2025-07-07 09:34:22  \n", "1206585     通过    NaN      0    NaN    NaN    NaN  2025-07-07 09:34:22  \n", "1206586     通过    NaN      0    NaN    NaN    NaN  2025-07-07 09:34:22  \n", "1206587     通过    NaN      0    NaN    NaN    NaN  2025-07-07 09:34:22  \n", "1206588     通过    NaN      0    NaN    NaN    NaN  2025-07-07 09:34:22  \n", "\n", "[1206589 rows x 51 columns]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["dfvat_api = pd.read_csv('/Users/<USER>/Library/CloudStorage/Box-Box/Planning Team/Tableau Auto-Refresh Raw Data/99 Hierarchy/SEI/input/发票查询明细_api.csv')\n", "dfvat_api"]}, {"cell_type": "code", "execution_count": 9, "id": "499492ab", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["发票日期最小值： 2023-07-03 00:00:00\n", "收票日期最小值： 2023-08-23 16:57:05\n"]}], "source": ["dfvat_api['发票日期'] = pd.to_datetime(dfvat_api['发票日期'])\n", "dfvat_api['收票日期'] = pd.to_datetime(dfvat_api['收票日期'])\n", "\n", "print(\"发票日期最小值：\", dfvat_api['发票日期'].min())\n", "print(\"收票日期最小值：\", dfvat_api['收票日期'].min())"]}, {"cell_type": "code", "execution_count": null, "id": "077db929", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "ML", "language": "python", "name": "ml"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.17"}}, "nbformat": 4, "nbformat_minor": 5}