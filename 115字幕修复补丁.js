// 115网盘DPlayer字幕修复补丁
// 专门解决字幕加载失败的问题

console.log('=== 115网盘字幕修复补丁 ===');

// 等待DPlayer加载
function waitForDPlayer(callback) {
    var checkCount = 0;
    var checkInterval = setInterval(function() {
        checkCount++;
        if (typeof unsafeWindow !== 'undefined' && unsafeWindow.dp && unsafeWindow.dp.video) {
            clearInterval(checkInterval);
            callback(unsafeWindow.dp);
        } else if (checkCount > 20) {
            clearInterval(checkInterval);
            console.error('❌ 未找到DPlayer，请确保在播放页面运行');
        }
    }, 500);
}

// 修复字幕功能
function fixSubtitleFunction(dp) {
    console.log('🔧 开始修复字幕功能...');
    
    // 保存原始的switchSubtitle函数（如果存在）
    var originalSwitchSubtitle = unsafeWindow.switchSubtitle;
    
    // 创建新的字幕切换函数
    unsafeWindow.switchSubtitle = function(url) {
        console.log('🎬 切换字幕:', url);
        
        try {
            if (url) {
                // 加载字幕
                console.log('📥 开始加载字幕...');
                dp.notice('正在加载字幕...', 1000);
                
                // 清理现有字幕
                clearExistingSubtitles(dp);
                
                // 使用GM_xmlhttpRequest获取字幕内容（避免CORS问题）
                if (typeof GM_xmlhttpRequest !== 'undefined') {
                    loadSubtitleWithGM(dp, url);
                } else {
                    // 降级到fetch
                    loadSubtitleWithFetch(dp, url);
                }
                
            } else {
                // 关闭字幕
                console.log('🔇 关闭字幕');
                clearExistingSubtitles(dp);
                dp.notice('字幕已关闭', 2000);
            }
        } catch (error) {
            console.error('❌ 字幕操作失败:', error);
            dp.notice('字幕操作失败: ' + error.message, 3000);
        }
        
        // 关闭字幕菜单
        closeSubtitleMenu();
    };
    
    console.log('✅ 字幕切换函数已修复');
}

// 清理现有字幕
function clearExistingSubtitles(dp) {
    // 禁用所有字幕轨道
    if (dp.video.textTracks) {
        for (var i = 0; i < dp.video.textTracks.length; i++) {
            dp.video.textTracks[i].mode = 'disabled';
        }
    }
    
    // 移除所有track元素
    var tracks = dp.video.querySelectorAll('track');
    tracks.forEach(function(track) {
        track.remove();
    });
}

// 使用GM_xmlhttpRequest加载字幕（推荐方式）
function loadSubtitleWithGM(dp, url) {
    console.log('🌐 使用GM_xmlhttpRequest获取字幕...');
    
    GM_xmlhttpRequest({
        method: 'GET',
        url: url,
        headers: {
            'Accept': 'text/vtt,text/plain,*/*',
            'Cache-Control': 'no-cache'
        },
        onload: function(response) {
            if (response.status === 200) {
                console.log('✅ 字幕内容获取成功');
                processSubtitleContent(dp, response.responseText);
            } else {
                console.error('❌ 字幕获取失败:', response.status, response.statusText);
                dp.notice('字幕获取失败: HTTP ' + response.status, 3000);
            }
        },
        onerror: function(error) {
            console.error('❌ 字幕请求失败:', error);
            dp.notice('字幕请求失败', 3000);
            // 尝试降级方案
            loadSubtitleWithFetch(dp, url);
        }
    });
}

// 使用fetch加载字幕（降级方案）
function loadSubtitleWithFetch(dp, url) {
    console.log('🌐 使用fetch获取字幕...');
    
    fetch(url, {
        method: 'GET',
        credentials: 'include',
        headers: {
            'Accept': 'text/vtt,text/plain,*/*',
            'Cache-Control': 'no-cache'
        }
    }).then(function(response) {
        if (!response.ok) {
            throw new Error('HTTP ' + response.status + ': ' + response.statusText);
        }
        return response.text();
    }).then(function(subtitleText) {
        console.log('✅ 字幕内容获取成功');
        processSubtitleContent(dp, subtitleText);
    }).catch(function(error) {
        console.error('❌ fetch失败:', error);
        dp.notice('字幕加载失败: ' + error.message, 3000);
        // 最后尝试直接加载
        loadSubtitleDirect(dp, url);
    });
}

// 直接加载字幕（最后的尝试）
function loadSubtitleDirect(dp, url) {
    console.log('🔄 尝试直接加载字幕...');
    
    var track = document.createElement('track');
    track.kind = 'subtitles';
    track.src = url;
    track.srclang = 'zh-CN';
    track.label = '字幕';
    track.default = true;
    track.crossOrigin = 'anonymous';
    
    track.addEventListener('load', function() {
        console.log('✅ 直接加载成功');
        track.mode = 'showing';
        dp.notice('字幕已加载', 2000);
    });
    
    track.addEventListener('error', function(e) {
        console.error('❌ 直接加载也失败:', e);
        dp.notice('字幕加载完全失败', 3000);
    });
    
    dp.video.appendChild(track);
    
    setTimeout(function() {
        track.mode = 'showing';
    }, 100);
}

// 处理字幕内容
function processSubtitleContent(dp, subtitleText) {
    console.log('📝 处理字幕内容，长度:', subtitleText.length);
    console.log('📝 内容预览:', subtitleText.substring(0, 200));
    
    // 检查和转换字幕格式
    var vttContent = convertToVTT(subtitleText);
    
    // 创建data URL
    var dataUrl = 'data:text/vtt;charset=utf-8,' + encodeURIComponent(vttContent);
    
    // 创建字幕轨道
    var track = document.createElement('track');
    track.kind = 'subtitles';
    track.src = dataUrl;
    track.srclang = 'zh-CN';
    track.label = '字幕';
    track.default = true;
    
    track.addEventListener('load', function() {
        console.log('✅ 字幕轨道加载完成');
        track.mode = 'showing';
        
        setTimeout(function() {
            if (track.cues && track.cues.length > 0) {
                console.log('📊 字幕条数:', track.cues.length);
                dp.notice('字幕加载成功 (' + track.cues.length + ' 条)', 2000);
            } else {
                console.warn('⚠️ 字幕轨道无内容');
                dp.notice('字幕文件可能为空', 2000);
            }
        }, 500);
    });
    
    track.addEventListener('error', function(e) {
        console.error('❌ 字幕轨道错误:', e);
        dp.notice('字幕格式错误', 2000);
    });
    
    dp.video.appendChild(track);
    
    setTimeout(function() {
        track.mode = 'showing';
        console.log('👁️ 字幕已设置为显示');
    }, 100);
}

// 转换字幕格式为VTT
function convertToVTT(content) {
    // 如果已经是VTT格式
    if (content.trim().startsWith('WEBVTT')) {
        return content;
    }
    
    // 如果是SRT格式，转换为VTT
    if (content.includes('-->')) {
        console.log('🔄 转换SRT格式为VTT');
        return 'WEBVTT\n\n' + content;
    }
    
    // 其他格式，尝试作为VTT处理
    console.log('⚠️ 未知格式，尝试作为VTT处理');
    return 'WEBVTT\n\n' + content;
}

// 关闭字幕菜单
function closeSubtitleMenu() {
    if (typeof unsafeWindow.closeSubtitleMenu === 'function') {
        unsafeWindow.closeSubtitleMenu();
    } else {
        var overlay = document.getElementById('subtitle-overlay');
        if (overlay && overlay.parentElement) {
            overlay.parentElement.remove();
        }
    }
}

// 添加测试功能
function addTestFunctions(dp) {
    // 测试字幕
    unsafeWindow.testSubtitle = function() {
        var testVTT = 'WEBVTT\n\n00:00:01.000 --> 00:00:05.000\n测试字幕 - 修复成功！\n\n00:00:06.000 --> 00:00:10.000\n字幕功能正常工作';
        var dataUrl = 'data:text/vtt;charset=utf-8,' + encodeURIComponent(testVTT);
        unsafeWindow.switchSubtitle(dataUrl);
    };
    
    // 清除字幕
    unsafeWindow.clearSubtitle = function() {
        unsafeWindow.switchSubtitle(null);
    };
    
    console.log('🧪 测试函数已添加: testSubtitle(), clearSubtitle()');
}

// 主函数
function main() {
    waitForDPlayer(function(dp) {
        console.log('✅ 找到DPlayer:', dp);
        
        // 修复字幕功能
        fixSubtitleFunction(dp);
        
        // 添加测试功能
        addTestFunctions(dp);
        
        // 显示成功消息
        dp.notice('字幕功能修复完成！', 3000);
        
        console.log('🎉 字幕修复补丁安装完成！');
        console.log('💡 可用命令:');
        console.log('   testSubtitle() - 测试字幕功能');
        console.log('   clearSubtitle() - 清除字幕');
    });
}

// 启动修复
main();
