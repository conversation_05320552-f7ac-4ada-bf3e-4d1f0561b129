<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单字幕按钮测试</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/dplayer@1.27.1/dist/DPlayer.min.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #000;
            color: #fff;
            font-family: Arial, sans-serif;
        }
        #Dplayer {
            width: 100%;
            height: 400px;
            margin: 20px 0;
        }
        .info {
            background: #111;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 14px;
            max-height: 200px;
            overflow-y: auto;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            background: #333;
            color: #fff;
            border: 1px solid #555;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #555;
        }
    </style>
</head>
<body>
    <h1>简单字幕按钮测试</h1>
    
    <div class="info">
        <div>🎯 测试DPlayer字幕按钮的原生功能</div>
        <div>📝 步骤：1. 点击"加载字幕" 2. 观察字幕按钮状态 3. 点击字幕按钮测试</div>
    </div>
    
    <div id="Dplayer"></div>
    
    <div>
        <button onclick="loadSubtitle()">加载字幕</button>
        <button onclick="checkButton()">检查按钮状态</button>
        <button onclick="triggerShow()">触发显示事件</button>
        <button onclick="triggerHide()">触发隐藏事件</button>
    </div>
    
    <div class="info" id="log">等待操作...</div>

    <script src="https://cdn.jsdelivr.net/npm/dplayer@1.27.1/dist/DPlayer.min.js"></script>
    <script>
        function log(msg) {
            const logDiv = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            logDiv.innerHTML += `<div>[${time}] ${msg}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(msg);
        }

        // 测试字幕内容
        const testSubtitle = `WEBVTT

00:00:01.000 --> 00:00:05.000
🎬 测试字幕内容

00:00:06.000 --> 00:00:10.000
✨ 检查按钮状态是否正确

00:00:11.000 --> 00:00:15.000
🔄 验证事件触发机制`;

        // 初始化DPlayer - 带字幕配置
        const dp = new DPlayer({
            container: document.getElementById('Dplayer'),
            screenshot: true,
            volume: 0.7,
            video: {
                url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
                pic: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/images/BigBuckBunny.jpg'
            },
            subtitle: {
                url: 'data:text/vtt;charset=utf-8,' + encodeURIComponent(testSubtitle),
                type: 'webvtt'
            }
        });

        log('DPlayer 初始化完成，带有字幕配置');

        // 监听DPlayer事件
        dp.on('loadeddata', function() {
            log('📹 视频数据加载完成');
            setTimeout(checkButton, 1000);
        });

        dp.events.on('subtitle_show', function() {
            log('🎯 DPlayer事件: subtitle_show 被触发');
        });

        dp.events.on('subtitle_hide', function() {
            log('🎯 DPlayer事件: subtitle_hide 被触发');
        });

        function loadSubtitle() {
            log('🚀 开始加载字幕');
            
            // 清理现有字幕
            if (dp.video.textTracks) {
                for (var i = 0; i < dp.video.textTracks.length; i++) {
                    dp.video.textTracks[i].mode = 'disabled';
                }
            }
            
            // 创建新的字幕轨道
            const dataUrl = 'data:text/vtt;charset=utf-8,' + encodeURIComponent(testSubtitle);
            const track = document.createElement('track');
            track.kind = 'subtitles';
            track.src = dataUrl;
            track.srclang = 'zh-CN';
            track.label = '测试字幕';
            track.default = true;
            
            dp.video.appendChild(track);
            
            track.addEventListener('load', function() {
                log('✅ 字幕轨道加载完成');
                track.mode = 'showing';
                
                setTimeout(function() {
                    if (track.cues && track.cues.length > 0) {
                        log(`📝 字幕内容: ${track.cues.length} 条cues`);
                        log(`🎯 字幕轨道模式: ${track.mode}`);
                        
                        // 触发字幕显示事件
                        dp.events.trigger('subtitle_show');
                        
                        setTimeout(checkButton, 500);
                    }
                }, 500);
            });
            
            // 立即设置为显示模式
            setTimeout(function() {
                track.mode = 'showing';
                log(`🎯 字幕已启用: ${track.mode}`);
            }, 100);
        }

        function checkButton() {
            log('🔍 检查字幕按钮状态');
            
            const button = document.querySelector('.dplayer-subtitle-button');
            if (button) {
                const inner = button.querySelector('.dplayer-subtitle-button-inner');
                const balloon = button.getAttribute('data-balloon');
                const opacity = inner ? inner.style.opacity : 'undefined';
                
                log(`✅ 字幕按钮存在`);
                log(`🎨 按钮透明度: ${opacity}`);
                log(`💬 提示文本: ${balloon}`);
                
                // 检查实际字幕状态
                let actualVisible = false;
                if (dp.video.textTracks && dp.video.textTracks.length > 0) {
                    for (let i = 0; i < dp.video.textTracks.length; i++) {
                        const track = dp.video.textTracks[i];
                        if (track.mode === 'showing' && track.cues && track.cues.length > 0) {
                            actualVisible = true;
                            break;
                        }
                    }
                }
                
                log(`👁️ 实际字幕可见: ${actualVisible}`);
                
                // 判断状态是否一致
                const buttonShowsHidden = (opacity === '0.4' || balloon === 'show-subs');
                const stateConsistent = (actualVisible && !buttonShowsHidden) || (!actualVisible && buttonShowsHidden);
                
                if (stateConsistent) {
                    log('✅ 按钮状态与实际状态一致');
                } else {
                    log('❌ 按钮状态与实际状态不一致！');
                }
            } else {
                log('❌ 字幕按钮不存在');
            }
        }

        function triggerShow() {
            log('🔧 手动触发 subtitle_show 事件');
            dp.events.trigger('subtitle_show');
            setTimeout(checkButton, 100);
        }

        function triggerHide() {
            log('🔧 手动触发 subtitle_hide 事件');
            dp.events.trigger('subtitle_hide');
            setTimeout(checkButton, 100);
        }

        // 延迟初始检查
        setTimeout(function() {
            log('🚀 开始初始检查');
            checkButton();
        }, 3000);
    </script>
</body>
</html>
