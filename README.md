# 115Lorax增强功能模块

这是从115lorax脚本中剥离出来的增强功能，可以独立运行。主要包含Mac VLC播放器支持和DPlayer播放列表调试信息等功能。

## 🎯 主要功能

### 1. Mac VLC播放器支持
- 支持直接调用Mac系统上的VLC播放器播放115网盘视频
- 自动尝试VLC协议调用
- 提供备用方案（复制链接到剪贴板）
- 智能错误处理和用户提示

### 2. DPlayer播放列表调试信息
- 在控制台打印详细的M3U8播放列表信息
- 显示所有可用的视频清晰度选项
- 便于调试和分析视频播放问题
- 可开关的调试模式

### 3. 增强的115网盘集成
- 在115网盘文件列表中添加VLC播放按钮
- 添加DPlayer调试播放按钮
- 无缝集成到现有的115网盘界面

## 📦 文件说明

- `115lorax_enhanced_features.js` - 主要的用户脚本文件
- `test_enhanced_features.html` - 功能测试页面
- `README.md` - 说明文档

## 🚀 安装和使用

### 安装步骤

1. **安装用户脚本管理器**
   - Chrome/Edge: 安装 [Tampermonkey](https://tampermonkey.net/)
   - Firefox: 安装 [Greasemonkey](https://addons.mozilla.org/firefox/addon/greasemonkey/)

2. **安装脚本**
   - 打开 `115lorax_enhanced_features.js` 文件
   - 复制全部内容
   - 在Tampermonkey中创建新脚本并粘贴
   - 保存并启用脚本

3. **验证安装**
   - 打开 `test_enhanced_features.html` 进行功能测试
   - 访问115网盘查看是否有新的播放按钮

### 使用方法

#### 在115网盘中使用
1. 登录115网盘并进入文件列表
2. 鼠标悬停在视频文件上
3. 点击新增的"VLC播放"或"调试播放"按钮

#### 通过API调用
```javascript
// 使用VLC播放器播放视频
LoraxEnhanced.playVideo('pickcode123', '视频名称.mp4', 'VLC');

// 使用DPlayer播放视频
LoraxEnhanced.playVideo('pickcode123', '视频名称.mp4', 'Dplayer');

// 直接调用VLC播放器
LoraxEnhanced.callMacVLC('http://video.url/video.mp4', '视频名称');

// 打印DPlayer调试信息
LoraxEnhanced.printDPlayerInfo(m3u8List, '视频名称');
```

## ⚙️ 配置选项

脚本提供了以下配置选项，可通过Tampermonkey菜单或API修改：

- `enableVLC`: 启用/禁用VLC播放器支持
- `enableDPlayerDebug`: 启用/禁用DPlayer调试信息
- `defaultPlayer`: 默认播放器类型

### 通过菜单配置
1. 点击Tampermonkey图标
2. 选择"VLC播放器开关"或"DPlayer调试开关"
3. 根据提示进行配置

### 通过代码配置
```javascript
// 修改配置
LoraxEnhanced.config.enableVLC = true;
LoraxEnhanced.config.enableDPlayerDebug = true;
LoraxEnhanced.config.defaultPlayer = 'VLC';
```

## 🧪 测试功能

### 使用测试页面
1. 在浏览器中打开 `test_enhanced_features.html`
2. 确保用户脚本已安装并启用
3. 点击各种测试按钮验证功能
4. 查看控制台输出和页面日志

### 测试项目
- VLC协议调用测试
- VLC备用方案测试
- DPlayer调试信息测试
- M3U8解析测试
- 模拟视频播放测试

## 🔧 技术细节

### VLC播放器调用机制
1. **协议调用**: 尝试使用 `vlc://` 协议直接调用VLC
2. **错误处理**: 如果协议调用失败，自动切换到备用方案
3. **备用方案**: 复制视频链接到剪贴板并提供详细使用说明

### DPlayer调试信息
1. **M3U8解析**: 解析115网盘返回的M3U8播放列表
2. **信息格式化**: 将播放列表信息格式化输出到控制台
3. **清晰度识别**: 自动识别不同清晰度选项

### 115网盘集成
1. **DOM监听**: 监听文件列表的鼠标悬停事件
2. **按钮注入**: 动态添加播放按钮到文件操作区域
3. **事件绑定**: 绑定点击事件处理播放请求

## 🐛 故障排除

### 常见问题

1. **VLC播放器无法调用**
   - 确保Mac系统已安装VLC播放器
   - 检查VLC是否在Applications文件夹中
   - 尝试手动注册VLC协议

2. **脚本无法加载**
   - 检查Tampermonkey是否已启用
   - 确认脚本匹配规则是否正确
   - 查看浏览器控制台是否有错误信息

3. **按钮不显示**
   - 确认在115网盘的文件列表页面
   - 检查是否为视频文件
   - 尝试刷新页面

### 调试方法
1. 打开浏览器控制台（F12）
2. 查看是否有错误信息
3. 运行 `LoraxEnhanced.testFeatures()` 进行自检
4. 使用测试页面验证各项功能

## 📝 更新日志

### v1.0 (2024-01-XX)
- 从115lorax主脚本中剥离增强功能
- 实现Mac VLC播放器支持
- 添加DPlayer播放列表调试信息
- 创建独立的测试页面
- 提供完整的API接口

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 📄 许可证

本项目基于GPL许可证开源，继承自原115优化大师项目。

## 🙏 致谢

- 感谢原作者 zxf10608 的115优化大师项目
- 感谢所有为115网盘优化做出贡献的开发者

---

**注意**: 这个脚本是从完整的115lorax脚本中剥离出来的增强功能模块，专注于Mac VLC播放器支持和DPlayer调试功能。如果需要完整的115网盘优化功能，请使用原始的115lorax脚本。
