# 115网盘DPlayer字幕功能说明

## 功能概述

已成功为115网盘DPlayer播放器添加了完整的字幕选择功能，现在可以自动获取并显示115网盘视频的字幕文件。

## 新增功能

### 1. 自动字幕获取
- 播放视频时自动调用115网盘字幕API获取字幕列表
- API地址：`https://webapi.115.com/movies/subtitle?pickcode={视频pickcode}`
- 支持多种字幕格式（主要是WebVTT格式）

### 2. 智能字幕选择
- **自动选择**：优先选择中文字幕，如果没有则选择第一个可用字幕
- **中文优先**：只选择中文字幕
- **英文优先**：只选择英文字幕
- **关闭字幕**：不显示任何字幕

### 3. 用户界面增强
- 在DPlayer右键菜单中新增"字幕选择"选项
- 提供友好的字幕切换弹窗界面
- 支持实时字幕切换，无需重新加载视频

### 4. 配置选项
在脚本设置中新增两个配置项：
- **启用字幕功能**：开关字幕功能（默认开启）
- **默认字幕语言**：设置默认字幕选择策略（默认自动选择）

## 使用方法

### 基本使用
1. 确保脚本已正确安装并启用
2. 访问115网盘，找到有字幕的视频文件
3. 点击"Dp播放"按钮播放视频
4. 如果视频有字幕，会根据设置自动加载

### 字幕切换
1. 在播放器上右键点击
2. 选择"字幕选择"选项
3. 在弹出的菜单中选择想要的字幕
4. 或选择"关闭字幕"来隐藏字幕

### 配置设置
1. 点击脚本管理器中的"DPlayer设置"
2. 找到字幕相关配置：
   - 启用字幕功能：勾选以启用字幕功能
   - 默认字幕语言：选择合适的默认策略

## 技术实现

### 核心函数
- `getSubtitles(pid)`：获取指定视频的字幕列表
- `showSubtitleMenu(dp, subtitles)`：显示字幕选择菜单
- `switchSubtitle(url)`：切换到指定字幕
- `initDPlayer(m3u8, subtitles)`：初始化DPlayer并配置字幕

### 字幕配置
```javascript
subtitle: {
    url: selectedSubtitle.url,
    type: selectedSubtitle.type,
    fontSize: '20px',
    bottom: '40px',
    color: '#fff'
}
```

### API调用
```javascript
var href = 'https://webapi.115.com/movies/subtitle?pickcode=' + pid;
AjaxCall(href, function(error, htmlTxt) {
    // 处理字幕数据
});
```

## 兼容性

- ✅ 完全兼容原有的DPlayer功能
- ✅ 不影响视频播放性能
- ✅ 支持所有清晰度切换
- ✅ 保持原有的右键菜单功能
- ✅ 兼容播放记录同步功能

## 错误处理

- 网络错误时优雅降级，不影响视频播放
- 字幕文件不存在时不显示字幕选择选项
- API调用失败时在控制台输出错误信息
- 字幕解析失败时自动跳过

## 测试验证

已通过以下测试：
- ✅ 配置项正确性测试
- ✅ 字幕数据解析测试
- ✅ 字幕选择逻辑测试
- ✅ DPlayer配置生成测试
- ✅ 语法检查测试

## 注意事项

1. **字幕文件要求**：视频必须在115网盘中有对应的字幕文件
2. **网络连接**：需要正常的网络连接来获取字幕数据
3. **浏览器兼容**：建议使用现代浏览器以获得最佳体验
4. **脚本权限**：确保脚本有访问115网盘API的权限

## 更新内容

相比原版本，新增了以下文件和修改：
- 修改：`115-dplayer-only.js` - 添加字幕功能
- 新增：`test-subtitle.html` - 字幕功能测试页面
- 新增：`test-script.js` - 自动化测试脚本
- 新增：`CHANGELOG.md` - 更新日志
- 更新：`README-DPlayer.md` - 使用说明

现在您的115网盘DPlayer播放器已经具备完整的字幕选择功能！
