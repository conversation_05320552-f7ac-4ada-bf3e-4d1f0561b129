<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>字幕按钮点击功能测试</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/dplayer@1.27.1/dist/DPlayer.min.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #000;
            color: #fff;
            font-family: Arial, sans-serif;
        }
        #Dplayer {
            width: 100%;
            height: 400px;
            margin: 20px 0;
        }
        .info {
            background: #111;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            background: #333;
            color: #fff;
            border: 1px solid #555;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #555;
        }
        .success {
            color: #0f0;
        }
        .error {
            color: #f00;
        }
        .warning {
            color: #ff0;
        }
    </style>
</head>
<body>
    <h1>字幕按钮点击功能测试</h1>
    
    <div class="info">
        <div>🎯 <strong>测试重点</strong>：验证字幕按钮点击后能否正确切换字幕显示/隐藏</div>
        <div>📝 <strong>操作步骤</strong>：</div>
        <div>&nbsp;&nbsp;1. 点击"初始化测试"加载字幕</div>
        <div>&nbsp;&nbsp;2. 点击字幕按钮测试切换功能</div>
        <div>&nbsp;&nbsp;3. 观察字幕是否正确显示/隐藏</div>
    </div>
    
    <div id="Dplayer"></div>
    
    <div>
        <button onclick="initTest()">初始化测试</button>
        <button onclick="manualToggle()">手动切换</button>
        <button onclick="checkAll()">检查状态</button>
        <button onclick="clearLog()">清空日志</button>
    </div>
    
    <div class="info" id="log">等待操作...</div>

    <script src="https://cdn.jsdelivr.net/npm/dplayer@1.27.1/dist/DPlayer.min.js"></script>
    <script>
        function log(msg, type = '') {
            const logDiv = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            const className = type ? ` class="${type}"` : '';
            logDiv.innerHTML += `<div${className}>[${time}] ${msg}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(msg);
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '日志已清空<br>';
        }

        // 测试字幕内容
        const testSubtitle = `WEBVTT

00:00:01.000 --> 00:00:05.000
🎬 点击测试字幕

00:00:06.000 --> 00:00:10.000
✨ 验证切换功能

00:00:11.000 --> 00:00:15.000
🔄 测试按钮响应

00:00:16.000 --> 00:00:20.000
✅ 功能验证完成`;

        // 初始化DPlayer
        const dp = new DPlayer({
            container: document.getElementById('Dplayer'),
            screenshot: true,
            volume: 0.7,
            video: {
                url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
                pic: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/images/BigBuckBunny.jpg'
            },
            subtitle: {
                url: 'data:text/vtt;charset=utf-8,WEBVTT%0A%0A00%3A00%3A01.000%20--%3E%2000%3A00%3A02.000%0A%E5%AD%97%E5%B9%95%E5%8A%A0%E8%BD%BD%E4%B8%AD...',
                type: 'webvtt'
            }
        });

        log('DPlayer 初始化完成');

        // 监听DPlayer事件
        dp.events.on('subtitle_show', function() {
            log('🎯 DPlayer事件: subtitle_show 被触发', 'success');
        });

        dp.events.on('subtitle_hide', function() {
            log('🎯 DPlayer事件: subtitle_hide 被触发', 'success');
        });

        // 切换字幕显示状态
        function toggleSubtitleDisplay() {
            log('🔄 执行字幕切换');
            
            // 检查当前字幕状态
            var hasVisibleSubtitles = false;
            
            if (dp.video.textTracks && dp.video.textTracks.length > 0) {
                for (var i = 0; i < dp.video.textTracks.length; i++) {
                    var track = dp.video.textTracks[i];
                    if (track.mode === 'showing') {
                        hasVisibleSubtitles = true;
                        break;
                    }
                }
            }
            
            log(`👁️ 切换前字幕状态: ${hasVisibleSubtitles ? '显示' : '隐藏'}`);
            
            // 切换字幕显示状态
            if (hasVisibleSubtitles) {
                // 隐藏所有字幕
                for (var i = 0; i < dp.video.textTracks.length; i++) {
                    dp.video.textTracks[i].mode = 'disabled';
                }
                log('🙈 字幕已隐藏', 'success');
                dp.events.trigger('subtitle_hide');
            } else {
                // 显示字幕
                var hasEnabledAny = false;
                for (var i = 0; i < dp.video.textTracks.length; i++) {
                    var track = dp.video.textTracks[i];
                    if (track.cues && track.cues.length > 0) {
                        track.mode = 'showing';
                        hasEnabledAny = true;
                        log(`👁️ 启用字幕轨道: ${track.label}`, 'success');
                        break;
                    }
                }
                
                if (hasEnabledAny) {
                    log('👁️ 字幕已显示', 'success');
                    dp.events.trigger('subtitle_show');
                } else {
                    log('⚠️ 没有可用的字幕轨道', 'warning');
                    dp.events.trigger('subtitle_hide');
                }
            }
        }

        // 更新字幕按钮状态
        function updateSubtitleButtonState() {
            // 检查当前字幕状态
            var hasVisibleSubtitles = false;
            if (dp.video.textTracks && dp.video.textTracks.length > 0) {
                for (var i = 0; i < dp.video.textTracks.length; i++) {
                    var track = dp.video.textTracks[i];
                    if (track.mode === 'showing' && track.cues && track.cues.length > 0) {
                        hasVisibleSubtitles = true;
                        break;
                    }
                }
            }
            
            // 使用DPlayer的事件系统来更新按钮状态
            if (hasVisibleSubtitles) {
                dp.events.trigger('subtitle_show');
            } else {
                dp.events.trigger('subtitle_hide');
            }
        }

        // 设置字幕按钮处理器
        function setupSubtitleButtonHandler() {
            log('🎬 设置字幕按钮处理器');
            
            var subtitleButton = document.querySelector('.dplayer-subtitle-button');
            if (!subtitleButton) {
                log('⚠️ 未找到字幕按钮，延迟重试', 'warning');
                setTimeout(setupSubtitleButtonHandler, 1000);
                return;
            }
            
            log(`✅ 找到字幕按钮: ${subtitleButton.className}`);
            
            // 移除DPlayer原生的点击事件监听器
            var newButton = subtitleButton.cloneNode(true);
            subtitleButton.parentNode.replaceChild(newButton, subtitleButton);
            
            // 添加自定义的点击事件监听器
            newButton.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                log('🎯 字幕按钮被点击！', 'success');
                toggleSubtitleDisplay();
                
                // 延迟更新按钮状态
                setTimeout(function() {
                    updateSubtitleButtonState();
                    checkAll();
                }, 100);
            });
            
            log('✅ 字幕按钮自定义事件监听器已设置', 'success');
            
            // 更新初始状态
            updateSubtitleButtonState();
        }

        function initTest() {
            log('🚀 开始初始化测试');
            
            // 清理现有字幕
            if (dp.video.textTracks) {
                for (var i = 0; i < dp.video.textTracks.length; i++) {
                    dp.video.textTracks[i].mode = 'disabled';
                }
            }
            var existingTracks = dp.video.querySelectorAll('track');
            existingTracks.forEach(function(track) {
                track.remove();
            });
            
            // 创建新的字幕轨道
            const dataUrl = 'data:text/vtt;charset=utf-8,' + encodeURIComponent(testSubtitle);
            const track = document.createElement('track');
            track.kind = 'subtitles';
            track.src = dataUrl;
            track.srclang = 'zh-CN';
            track.label = '测试字幕';
            track.default = true;
            
            dp.video.appendChild(track);
            
            track.addEventListener('load', function() {
                log('✅ 字幕轨道加载完成');
                track.mode = 'showing';
                
                setTimeout(function() {
                    if (track.cues && track.cues.length > 0) {
                        log(`📝 字幕内容: ${track.cues.length} 条cues`);
                        
                        // 设置按钮处理器
                        setTimeout(function() {
                            setupSubtitleButtonHandler();
                            checkAll();
                        }, 500);
                    }
                }, 500);
            });
            
            setTimeout(function() {
                track.mode = 'showing';
            }, 100);
        }

        function manualToggle() {
            log('🔧 手动切换字幕');
            toggleSubtitleDisplay();
            setTimeout(checkAll, 200);
        }

        function checkAll() {
            log('🔍 检查所有状态');
            
            const button = document.querySelector('.dplayer-subtitle-button');
            if (button) {
                const inner = button.querySelector('.dplayer-subtitle-button-inner');
                const balloon = button.getAttribute('data-balloon');
                const opacity = inner ? inner.style.opacity : '1';
                
                log(`🎨 按钮透明度: ${opacity}`);
                log(`💬 提示文本: ${balloon}`);
                
                // 检查实际字幕状态
                let actualVisible = false;
                if (dp.video.textTracks && dp.video.textTracks.length > 0) {
                    for (let i = 0; i < dp.video.textTracks.length; i++) {
                        const track = dp.video.textTracks[i];
                        if (track.mode === 'showing' && track.cues && track.cues.length > 0) {
                            actualVisible = true;
                            break;
                        }
                    }
                }
                
                log(`👁️ 实际字幕可见: ${actualVisible}`);
                
                // 判断状态是否一致
                const buttonShowsActive = (opacity !== '0.4' && balloon === 'hide-subs');
                const stateConsistent = (actualVisible && buttonShowsActive) || (!actualVisible && !buttonShowsActive);
                
                if (stateConsistent) {
                    log('✅ 按钮状态与实际状态一致', 'success');
                } else {
                    log('❌ 按钮状态与实际状态不一致', 'error');
                }
            } else {
                log('❌ 字幕按钮不存在', 'error');
            }
        }

        // 延迟初始检查
        setTimeout(function() {
            log('🚀 页面加载完成，等待用户操作');
            setTimeout(checkAll, 2000);
        }, 1000);
    </script>
</body>
</html>
