/**
 * 115Lorax增强功能演示脚本
 * 这个文件展示了如何使用剥离出来的核心功能
 */

// 模拟115网盘环境和依赖
const mockStorage = {};
const mockEnvironment = {
    // 模拟GM函数
    GM_setValue: (key, value) => mockStorage[key] = value,
    GM_getValue: (key, defaultValue) => {
        return mockStorage.hasOwnProperty(key) ? mockStorage[key] : defaultValue;
    },
    GM_setClipboard: (text) => {
        console.log('📋 已复制到剪贴板:', text);
        // 在真实环境中这会复制到系统剪贴板
    },
    
    // 模拟toastr通知
    toastr: {
        success: (message, title) => console.log(`✅ ${title}: ${message}`),
        error: (message, title) => console.log(`❌ ${title}: ${message}`),
        info: (message, title) => console.log(`ℹ️ ${title}: ${message}`),
        warning: (message, title) => console.log(`⚠️ ${title}: ${message}`)
    }
};

// 将模拟环境添加到全局
Object.assign(global || window, mockEnvironment);

/**
 * 核心功能1: Mac VLC播放器调用
 */
function callMacVLC(videoUrl, videoName) {
    console.log('🎬 调用Mac VLC播放器:', videoUrl);
    
    try {
        // 在真实环境中，这会尝试调用VLC协议
        console.log(`尝试VLC协议: vlc://${encodeURIComponent(videoUrl)}`);
        
        // 模拟协议调用成功
        if (Math.random() > 0.3) { // 70%成功率
            console.log('✅ VLC协议调用成功');
            toastr.success('正在调用VLC播放器...', 'VLC播放器');
            return true;
        } else {
            throw new Error('VLC协议未注册');
        }
    } catch (e) {
        console.log('⚠️ VLC协议调用失败，使用备用方案');
        
        // 备用方案：复制链接到剪贴板
        GM_setClipboard(videoUrl);
        
        const helpText = `VLC协议未注册，视频地址已复制到剪贴板。请：
1. 打开VLC播放器
2. 点击菜单 文件 > 打开网络串流 (⌘+N)
3. 粘贴地址并播放

或者手动注册VLC协议：
打开终端执行: /Applications/VLC.app/Contents/MacOS/VLC --intf=rc`;
        
        toastr.info(helpText, 'VLC播放器调用');
        console.log('📋 视频链接已复制到剪贴板');
        return false;
    }
}

/**
 * 核心功能2: DPlayer播放列表信息打印
 */
function printDPlayerInfo(m3u8List, videoName, enableDebug = true) {
    if (!enableDebug) {
        console.log('🔇 DPlayer调试信息已禁用');
        return;
    }
    
    console.log('=== DPlayer M3U8 播放列表 ===');
    console.log('🎥 视频名称:', videoName);
    console.log('📋 m3u8列表:', m3u8List);
    
    if (m3u8List && m3u8List.length > 0) {
        m3u8List.forEach((item, index) => {
            console.log(`📺 清晰度 ${index + 1}: ${item.name} - ${item.url}`);
        });
    } else {
        console.log('⚠️ 没有可用的播放列表');
    }
    console.log('=============================');
}

/**
 * 核心功能3: M3U8播放列表解析（模拟）
 */
function parseM3U8Content(m3u8Content) {
    console.log('🔍 解析M3U8内容...');
    
    // 模拟解析M3U8内容
    const lines = m3u8Content.split('\n');
    const qualities = [];
    
    // 模拟115网盘的清晰度标识
    const qualityMap = {
        '"YH"': '原画',
        '"BD"': '4K',
        '"UD"': '蓝光',
        '"HD"': '超清',
        '"SD"': '高清',
        '"3G"': '标清'
    };
    
    lines.forEach((line, index) => {
        Object.keys(qualityMap).forEach(key => {
            if (line.includes(key)) {
                const nextLine = lines[index + 1];
                if (nextLine && nextLine.includes('.m3u8')) {
                    qualities.push({
                        name: qualityMap[key],
                        url: nextLine.trim(),
                        type: 'hls'
                    });
                }
            }
        });
    });
    
    return qualities;
}

/**
 * 演示函数：模拟完整的播放流程
 */
function demonstratePlayback() {
    console.log('\n🚀 开始演示115Lorax增强功能...\n');
    
    // 模拟视频信息
    const videoInfo = {
        pickCode: 'demo123456',
        fileName: '示例电影.mp4',
        m3u8Url: 'https://115.com/api/video/m3u8/demo123456.m3u8'
    };
    
    // 模拟M3U8内容
    const mockM3u8Content = `#EXTM3U
#EXT-X-VERSION:3
#EXT-X-TARGETDURATION:10
"HD"
https://video.115.com/hd/demo123456.m3u8
"SD"
https://video.115.com/sd/demo123456.m3u8
"3G"
https://video.115.com/3g/demo123456.m3u8
#EXT-X-ENDLIST`;
    
    // 1. 解析M3U8播放列表
    console.log('📋 步骤1: 解析M3U8播放列表');
    const m3u8List = parseM3U8Content(mockM3u8Content);
    
    // 2. 打印调试信息
    console.log('\n🐛 步骤2: 打印DPlayer调试信息');
    printDPlayerInfo(m3u8List, videoInfo.fileName);
    
    // 3. 尝试VLC播放器调用
    console.log('\n🎬 步骤3: 调用VLC播放器');
    if (m3u8List.length > 0) {
        const bestQuality = m3u8List[0]; // 选择最高清晰度
        callMacVLC(bestQuality.url, videoInfo.fileName);
    }
    
    console.log('\n✅ 演示完成！');
}

/**
 * 配置管理演示
 */
function demonstrateConfig() {
    console.log('\n⚙️ 配置管理演示...\n');
    
    // 模拟配置对象
    const config = {
        enableVLC: GM_getValue('enableVLC', true),
        enableDPlayerDebug: GM_getValue('enableDPlayerDebug', true),
        defaultPlayer: GM_getValue('defaultPlayer', 'Dplayer')
    };
    
    console.log('📋 当前配置:', config);
    
    // 修改配置
    config.enableVLC = false;
    GM_setValue('enableVLC', config.enableVLC);
    console.log('🔧 已禁用VLC播放器');
    
    config.enableDPlayerDebug = false;
    GM_setValue('enableDPlayerDebug', config.enableDPlayerDebug);
    console.log('🔇 已禁用DPlayer调试');
    
    // 恢复配置
    config.enableVLC = true;
    config.enableDPlayerDebug = true;
    GM_setValue('enableVLC', config.enableVLC);
    GM_setValue('enableDPlayerDebug', config.enableDPlayerDebug);
    console.log('🔄 已恢复默认配置');
}

/**
 * API使用演示
 */
function demonstrateAPI() {
    console.log('\n🔌 API使用演示...\n');
    
    // 模拟LoraxEnhanced API对象
    const LoraxEnhanced = {
        // 播放视频
        playVideo: function(pickCode, fileName, playerType = 'Dplayer') {
            console.log(`🎬 播放视频: ${fileName} (${pickCode}) 使用 ${playerType}`);
            
            // 模拟获取M3U8
            const mockM3u8 = [
                {name: '超清', url: `https://video.115.com/hd/${pickCode}.m3u8`, type: 'hls'},
                {name: '高清', url: `https://video.115.com/sd/${pickCode}.m3u8`, type: 'hls'}
            ];
            
            printDPlayerInfo(mockM3u8, fileName);
            
            if (playerType === 'VLC') {
                callMacVLC(mockM3u8[0].url, fileName);
            } else {
                console.log(`🎥 使用${playerType}播放器播放`);
            }
        },
        
        // 直接调用VLC
        callMacVLC: callMacVLC,
        
        // 打印调试信息
        printDPlayerInfo: printDPlayerInfo,
        
        // 配置对象
        config: {
            enableVLC: true,
            enableDPlayerDebug: true,
            defaultPlayer: 'Dplayer'
        }
    };
    
    // 演示API调用
    console.log('📞 API调用示例:');
    LoraxEnhanced.playVideo('test123', '测试视频.mp4', 'VLC');
    
    console.log('\n📞 直接调用VLC:');
    LoraxEnhanced.callMacVLC('http://example.com/video.mp4', '外部视频');
    
    console.log('\n📞 打印调试信息:');
    const testM3u8 = [
        {name: '4K', url: 'http://test.com/4k.m3u8', type: 'hls'},
        {name: '1080P', url: 'http://test.com/1080p.m3u8', type: 'hls'}
    ];
    LoraxEnhanced.printDPlayerInfo(testM3u8, 'API测试视频');
    
    return LoraxEnhanced;
}

/**
 * 主演示函数
 */
function runDemo() {
    console.log('🎯 115Lorax增强功能核心演示');
    console.log('=====================================');
    
    // 运行各种演示
    demonstratePlayback();
    demonstrateConfig();
    const api = demonstrateAPI();
    
    console.log('\n📚 演示总结:');
    console.log('1. ✅ Mac VLC播放器调用功能');
    console.log('2. ✅ DPlayer播放列表调试信息');
    console.log('3. ✅ M3U8内容解析');
    console.log('4. ✅ 配置管理');
    console.log('5. ✅ API接口');
    
    console.log('\n🔗 可用的API:');
    console.log('- LoraxEnhanced.playVideo(pickCode, fileName, playerType)');
    console.log('- LoraxEnhanced.callMacVLC(videoUrl, videoName)');
    console.log('- LoraxEnhanced.printDPlayerInfo(m3u8List, videoName)');
    
    return api;
}

// 如果在Node.js环境中运行
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        callMacVLC,
        printDPlayerInfo,
        parseM3U8Content,
        runDemo
    };
}

// 如果在浏览器环境中运行
if (typeof window !== 'undefined') {
    window.LoraxDemo = {
        callMacVLC,
        printDPlayerInfo,
        parseM3U8Content,
        runDemo
    };
    
    // 自动运行演示
    console.log('🌐 在浏览器中运行演示...');
    runDemo();
}

// 在Node.js中运行演示
if (typeof require !== 'undefined' && require.main === module) {
    runDemo();
}
