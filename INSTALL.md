# 115Lorax增强功能模块 - 安装指南

## 🚀 快速开始

### 第一步：安装用户脚本管理器

根据您的浏览器选择对应的扩展：

#### Chrome / Edge / Brave
1. 访问 [Tampermonkey官网](https://tampermonkey.net/)
2. 点击对应浏览器的下载链接
3. 安装扩展并启用

#### Firefox
1. 访问 [Greasemonkey插件页](https://addons.mozilla.org/firefox/addon/greasemonkey/)
2. 点击"添加到Firefox"
3. 安装并启用插件

#### Safari
1. 访问 [Userscripts插件页](https://apps.apple.com/app/userscripts/id1463298887)
2. 从App Store安装
3. 在Safari中启用扩展

### 第二步：安装脚本

#### 方法一：直接安装（推荐）
1. 打开 `115lorax_enhanced_features.js` 文件
2. 复制全部内容（Ctrl+A, Ctrl+C）
3. 打开Tampermonkey管理面板
4. 点击"创建新脚本"
5. 删除默认内容，粘贴复制的代码
6. 按 Ctrl+S 保存脚本
7. 确保脚本状态为"启用"

#### 方法二：从文件安装
1. 打开Tampermonkey管理面板
2. 点击"实用工具"标签
3. 在"从文件安装"区域选择 `115lorax_enhanced_features.js`
4. 点击"安装"

### 第三步：验证安装

#### 使用测试页面验证
1. 在浏览器中打开 `test_enhanced_features.html`
2. 页面加载后应该显示"✅ 115Lorax增强功能脚本已加载"
3. 点击"检查脚本状态"按钮
4. 如果显示绿色成功信息，说明安装成功

#### 在115网盘中验证
1. 登录 [115网盘](https://115.com)
2. 进入文件列表页面
3. 鼠标悬停在视频文件上
4. 应该能看到新增的"VLC播放"和"调试播放"按钮

## ⚙️ 配置设置

### 通过菜单配置
安装成功后，您可以通过以下方式配置功能：

1. 点击浏览器工具栏中的Tampermonkey图标
2. 在下拉菜单中找到以下选项：
   - "VLC播放器开关" - 启用/禁用VLC播放器支持
   - "DPlayer调试开关" - 启用/禁用调试信息输出

### 默认配置
脚本的默认配置如下：
- VLC播放器支持：启用
- DPlayer调试信息：启用
- 默认播放器：Dplayer

## 🎯 功能使用

### VLC播放器功能
1. **前提条件**：Mac系统需要安装VLC播放器
2. **使用方法**：
   - 在115网盘中点击"VLC播放"按钮
   - 或使用API：`LoraxEnhanced.callMacVLC(videoUrl, videoName)`
3. **备用方案**：如果VLC协议调用失败，会自动复制视频链接到剪贴板

### DPlayer调试功能
1. **查看调试信息**：
   - 打开浏览器控制台（F12）
   - 播放视频时会自动输出M3U8播放列表信息
2. **手动调用**：
   - 使用API：`LoraxEnhanced.printDPlayerInfo(m3u8List, videoName)`

## 🔧 高级配置

### 通过控制台配置
您可以在浏览器控制台中直接修改配置：

```javascript
// 禁用VLC播放器
LoraxEnhanced.config.enableVLC = false;

// 启用DPlayer调试
LoraxEnhanced.config.enableDPlayerDebug = true;

// 设置默认播放器
LoraxEnhanced.config.defaultPlayer = 'VLC';
```

### 持久化配置
配置会自动保存到浏览器本地存储，下次访问时会自动恢复。

## 🐛 故障排除

### 脚本无法加载
1. **检查Tampermonkey状态**
   - 确保Tampermonkey扩展已启用
   - 检查脚本是否在启用状态

2. **检查匹配规则**
   - 脚本应该在 `*.115.com` 域名下自动运行
   - 确认当前页面URL符合匹配规则

3. **查看错误信息**
   - 打开控制台（F12）
   - 查看是否有JavaScript错误

### VLC播放器无法调用
1. **检查VLC安装**
   ```bash
   # 在终端中检查VLC是否安装
   ls /Applications/VLC.app
   ```

2. **手动注册VLC协议**
   ```bash
   # 在终端中执行
   /Applications/VLC.app/Contents/MacOS/VLC --intf=rc
   ```

3. **使用备用方案**
   - 如果协议调用失败，会自动复制链接到剪贴板
   - 手动打开VLC播放器并粘贴链接

### 按钮不显示
1. **确认页面类型**
   - 功能仅在115网盘文件列表页面生效
   - 确认当前在正确的页面

2. **检查文件类型**
   - 按钮仅对视频文件显示
   - 确认文件具有视频属性

3. **刷新页面**
   - 尝试刷新页面重新加载脚本

## 📞 获取帮助

### 自助诊断
1. 运行测试页面：打开 `test_enhanced_features.html`
2. 执行自检命令：在控制台运行 `LoraxEnhanced.testFeatures()`
3. 查看API状态：运行 `console.log(LoraxEnhanced)`

### 常用调试命令
```javascript
// 检查脚本是否加载
console.log(typeof LoraxEnhanced);

// 查看当前配置
console.log(LoraxEnhanced.config);

// 测试VLC调用
LoraxEnhanced.callMacVLC('http://test.url/video.mp4', '测试视频');

// 测试调试信息
LoraxEnhanced.printDPlayerInfo([{name: '测试', url: 'test.m3u8'}], '测试');
```

## 🔄 更新脚本

当有新版本发布时：

1. 下载新的脚本文件
2. 在Tampermonkey中找到旧版本脚本
3. 点击"编辑"
4. 替换全部内容为新版本代码
5. 保存并刷新页面

## ⚠️ 注意事项

1. **兼容性**：脚本主要针对Mac系统的VLC播放器优化
2. **权限**：脚本需要访问115.com域名的权限
3. **依赖**：需要jQuery、Toastr、DPlayer等库的支持
4. **更新**：115网盘页面结构变化可能影响功能，请及时更新脚本

---

如果您在安装或使用过程中遇到问题，请参考故障排除部分或查看项目文档。
