// 字幕功能修复代码
// 这个代码片段用于替换原有的字幕切换功能

// 修复后的字幕切换函数
function createFixedSubtitleFunction(dp) {
    return function(url) {
        console.log('切换字幕:', url);
        console.log('DPlayer video元素:', dp.video);
        
        try {
            if (url) {
                // 切换到指定字幕
                console.log('加载字幕:', url);
                
                // 禁用所有现有字幕轨道
                if (dp.video.textTracks) {
                    for (var i = 0; i < dp.video.textTracks.length; i++) {
                        dp.video.textTracks[i].mode = 'disabled';
                    }
                }
                
                // 移除现有的track元素
                var existingTracks = dp.video.querySelectorAll('track');
                existingTracks.forEach(function(track) {
                    track.remove();
                });
                
                // 创建新的字幕轨道
                var track = document.createElement('track');
                track.kind = 'subtitles';
                track.src = url;
                track.srclang = 'zh-CN';
                track.label = '字幕';
                track.default = true;
                
                // 添加到video元素
                dp.video.appendChild(track);
                
                // 等待轨道加载完成后启用
                track.addEventListener('load', function() {
                    console.log('字幕轨道加载完成');
                    track.mode = 'showing';
                    dp.notice('字幕已加载', 2000);
                });
                
                track.addEventListener('error', function(e) {
                    console.error('字幕加载失败:', e);
                    dp.notice('字幕加载失败', 2000);
                });
                
                // 立即设置为显示模式
                setTimeout(function() {
                    track.mode = 'showing';
                    console.log('字幕已启用');
                    
                    // 检查字幕是否正确加载
                    setTimeout(function() {
                        if (dp.video.textTracks.length > 0) {
                            var activeTrack = dp.video.textTracks[0];
                            console.log('字幕轨道状态:', {
                                mode: activeTrack.mode,
                                readyState: activeTrack.readyState,
                                cues: activeTrack.cues ? activeTrack.cues.length : 0
                            });
                            
                            if (activeTrack.mode === 'showing' && activeTrack.cues && activeTrack.cues.length > 0) {
                                dp.notice('字幕加载成功', 2000);
                            } else if (activeTrack.cues && activeTrack.cues.length === 0) {
                                dp.notice('字幕文件为空或格式不支持', 3000);
                            }
                        }
                    }, 1000);
                }, 100);
                
                dp.notice('正在加载字幕...', 1000);
                
            } else {
                // 关闭字幕
                console.log('关闭字幕');
                
                // 禁用所有字幕轨道
                if (dp.video.textTracks) {
                    for (var i = 0; i < dp.video.textTracks.length; i++) {
                        dp.video.textTracks[i].mode = 'disabled';
                    }
                }
                
                // 移除所有track元素
                var tracks = dp.video.querySelectorAll('track');
                tracks.forEach(function(track) {
                    track.remove();
                });
                
                dp.notice('字幕已关闭', 2000);
            }
        } catch (error) {
            console.error('字幕操作失败:', error);
            dp.notice('字幕操作失败: ' + error.message, 3000);
        }
        
        // 关闭字幕选择菜单
        if (typeof closeSubtitleMenu === 'function') {
            closeSubtitleMenu();
        } else if (typeof unsafeWindow.closeSubtitleMenu === 'function') {
            unsafeWindow.closeSubtitleMenu();
        } else {
            // 手动关闭菜单
            var overlay = document.getElementById('subtitle-overlay');
            if (overlay && overlay.parentElement) {
                overlay.parentElement.remove();
            }
        }
    };
}

// 测试字幕功能的辅助函数
function testSubtitleFunction() {
    console.log('=== 字幕功能测试 ===');
    
    // 检查DPlayer是否存在
    if (typeof unsafeWindow.dp === 'undefined') {
        console.error('DPlayer对象不存在');
        return false;
    }
    
    var dp = unsafeWindow.dp;
    console.log('DPlayer对象:', dp);
    console.log('Video元素:', dp.video);
    
    // 检查video元素
    if (!dp.video) {
        console.error('Video元素不存在');
        return false;
    }
    
    // 检查textTracks支持
    console.log('TextTracks支持:', !!dp.video.textTracks);
    console.log('当前字幕轨道数量:', dp.video.textTracks ? dp.video.textTracks.length : 0);
    
    // 测试字幕切换函数
    var testSubtitleUrl = 'data:text/vtt;charset=utf-8,WEBVTT%0A%0A00%3A00%3A01.000%20--%3E%2000%3A00%3A05.000%0A测试字幕';
    
    console.log('测试字幕URL:', testSubtitleUrl);
    
    // 创建修复后的字幕函数
    var fixedSwitchSubtitle = createFixedSubtitleFunction(dp);
    
    // 替换原有的字幕切换函数
    unsafeWindow.switchSubtitle = fixedSwitchSubtitle;
    
    console.log('字幕功能修复完成');
    return true;
}

// 如果在浏览器环境中，自动应用修复
if (typeof window !== 'undefined' && typeof unsafeWindow !== 'undefined') {
    // 等待DPlayer加载完成
    var checkDPlayer = setInterval(function() {
        if (typeof unsafeWindow.dp !== 'undefined' && unsafeWindow.dp.video) {
            clearInterval(checkDPlayer);
            console.log('检测到DPlayer，应用字幕修复...');
            
            // 应用修复
            if (testSubtitleFunction()) {
                console.log('✅ 字幕功能修复成功！');
            } else {
                console.log('❌ 字幕功能修复失败');
            }
        }
    }, 1000);
    
    // 10秒后停止检测
    setTimeout(function() {
        clearInterval(checkDPlayer);
    }, 10000);
}

// 导出函数供外部使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        createFixedSubtitleFunction: createFixedSubtitleFunction,
        testSubtitleFunction: testSubtitleFunction
    };
}
