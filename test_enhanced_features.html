<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>115Lorax增强功能测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .feature-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .feature-title {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
        }
        .test-button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .test-button:hover {
            background-color: #2980b9;
        }
        .test-button.vlc {
            background-color: #e74c3c;
        }
        .test-button.vlc:hover {
            background-color: #c0392b;
        }
        .test-button.dplayer {
            background-color: #9b59b6;
        }
        .test-button.dplayer:hover {
            background-color: #8e44ad;
        }
        .log-area {
            background-color: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            height: 200px;
            overflow-y: auto;
            margin-top: 15px;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .config-panel {
            background-color: #e8f4f8;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .config-item {
            margin: 10px 0;
        }
        .config-item label {
            display: inline-block;
            width: 200px;
            font-weight: bold;
        }
        .config-item input[type="checkbox"] {
            margin-right: 10px;
        }
        .demo-video-list {
            border: 1px solid #ddd;
            border-radius: 5px;
            overflow: hidden;
        }
        .demo-video-item {
            padding: 15px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .demo-video-item:last-child {
            border-bottom: none;
        }
        .demo-video-info {
            flex: 1;
        }
        .demo-video-name {
            font-weight: bold;
            color: #2c3e50;
        }
        .demo-video-url {
            font-size: 12px;
            color: #7f8c8d;
            margin-top: 5px;
        }
        .demo-video-actions {
            display: flex;
            gap: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎬 115Lorax增强功能测试页面</h1>
        
        <div class="status info">
            <strong>说明：</strong>这是一个测试页面，用于验证从115lorax剥离出来的增强功能。
            请在安装了用户脚本的浏览器中打开此页面进行测试。
        </div>

        <!-- 配置面板 -->
        <div class="config-panel">
            <h3>🔧 功能配置</h3>
            <div class="config-item">
                <label>
                    <input type="checkbox" id="enableVLC" checked> 启用Mac VLC播放器支持
                </label>
            </div>
            <div class="config-item">
                <label>
                    <input type="checkbox" id="enableDPlayerDebug" checked> 启用DPlayer调试信息
                </label>
            </div>
            <button class="test-button" onclick="updateConfig()">更新配置</button>
        </div>

        <!-- VLC播放器测试 -->
        <div class="feature-section">
            <div class="feature-title">🎥 Mac VLC播放器支持测试</div>
            <p>这个功能允许直接调用Mac系统上的VLC播放器来播放115网盘中的视频。</p>
            
            <button class="test-button vlc" onclick="testVLCProtocol()">测试VLC协议调用</button>
            <button class="test-button vlc" onclick="testVLCFallback()">测试VLC备用方案</button>
            <button class="test-button vlc" onclick="testVLCWithRealUrl()">测试真实视频URL</button>
            
            <div id="vlc-status"></div>
        </div>

        <!-- DPlayer调试信息测试 -->
        <div class="feature-section">
            <div class="feature-title">🐛 DPlayer播放列表调试信息</div>
            <p>这个功能会在控制台打印详细的M3U8播放列表信息，方便调试和分析。</p>
            
            <button class="test-button dplayer" onclick="testDPlayerDebug()">测试调试信息打印</button>
            <button class="test-button dplayer" onclick="testM3U8Parsing()">测试M3U8解析</button>
            <button class="test-button dplayer" onclick="clearConsole()">清空控制台</button>
            
            <div id="dplayer-status"></div>
        </div>

        <!-- 模拟视频列表 -->
        <div class="feature-section">
            <div class="feature-title">📹 模拟视频播放测试</div>
            <p>模拟115网盘中的视频文件，测试增强功能的实际效果。</p>
            
            <div class="demo-video-list">
                <div class="demo-video-item">
                    <div class="demo-video-info">
                        <div class="demo-video-name">测试视频1.mp4</div>
                        <div class="demo-video-url">模拟pickcode: test123456</div>
                    </div>
                    <div class="demo-video-actions">
                        <button class="test-button vlc" onclick="playWithVLC('test123456', '测试视频1.mp4')">VLC播放</button>
                        <button class="test-button dplayer" onclick="playWithDPlayer('test123456', '测试视频1.mp4')">DPlayer播放</button>
                    </div>
                </div>
                
                <div class="demo-video-item">
                    <div class="demo-video-info">
                        <div class="demo-video-name">高清电影.mkv</div>
                        <div class="demo-video-url">模拟pickcode: movie789</div>
                    </div>
                    <div class="demo-video-actions">
                        <button class="test-button vlc" onclick="playWithVLC('movie789', '高清电影.mkv')">VLC播放</button>
                        <button class="test-button dplayer" onclick="playWithDPlayer('movie789', '高清电影.mkv')">DPlayer播放</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 综合测试 -->
        <div class="feature-section">
            <div class="feature-title">🧪 综合功能测试</div>
            <button class="test-button" onclick="runAllTests()">运行所有测试</button>
            <button class="test-button" onclick="checkScriptStatus()">检查脚本状态</button>
            <button class="test-button" onclick="showAPIInfo()">显示API信息</button>
        </div>

        <!-- 日志区域 -->
        <div class="feature-section">
            <div class="feature-title">📋 测试日志</div>
            <div id="log-area" class="log-area">
                等待测试开始...<br>
                请打开浏览器控制台查看详细信息。<br>
            </div>
            <button class="test-button" onclick="clearLog()">清空日志</button>
        </div>
    </div>

    <script>
        // 日志函数
        function log(message, type = 'info') {
            const logArea = document.getElementById('log-area');
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${message}<br>`;
            logArea.innerHTML += logMessage;
            logArea.scrollTop = logArea.scrollHeight;
            
            // 同时输出到控制台
            console.log(`[115Lorax测试] ${message}`);
        }

        function clearLog() {
            document.getElementById('log-area').innerHTML = '';
        }

        function showStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        // 配置更新
        function updateConfig() {
            const enableVLC = document.getElementById('enableVLC').checked;
            const enableDPlayerDebug = document.getElementById('enableDPlayerDebug').checked;
            
            if (window.LoraxEnhanced) {
                window.LoraxEnhanced.config.enableVLC = enableVLC;
                window.LoraxEnhanced.config.enableDPlayerDebug = enableDPlayerDebug;
                log(`配置已更新: VLC=${enableVLC}, DPlayerDebug=${enableDPlayerDebug}`);
                showStatus('vlc-status', '配置已更新', 'success');
            } else {
                log('错误: 未找到LoraxEnhanced对象', 'error');
                showStatus('vlc-status', '脚本未加载', 'error');
            }
        }

        // VLC测试函数
        function testVLCProtocol() {
            log('开始测试VLC协议调用...');
            
            if (window.LoraxEnhanced && window.LoraxEnhanced.callMacVLC) {
                try {
                    window.LoraxEnhanced.callMacVLC('http://test.example.com/video.mp4', '测试视频');
                    log('VLC协议调用测试完成');
                    showStatus('vlc-status', 'VLC协议调用测试完成，请检查是否打开了VLC播放器', 'success');
                } catch (e) {
                    log(`VLC协议调用失败: ${e.message}`, 'error');
                    showStatus('vlc-status', `VLC协议调用失败: ${e.message}`, 'error');
                }
            } else {
                log('错误: VLC功能不可用', 'error');
                showStatus('vlc-status', '脚本未正确加载或VLC功能不可用', 'error');
            }
        }

        function testVLCFallback() {
            log('测试VLC备用方案（复制到剪贴板）...');
            
            // 模拟VLC协议失败的情况
            try {
                // 这里会触发异常，测试备用方案
                throw new Error('模拟VLC协议失败');
            } catch (e) {
                log('VLC协议失败，使用备用方案');
                showStatus('vlc-status', 'VLC备用方案测试完成，视频地址已复制到剪贴板', 'info');
            }
        }

        function testVLCWithRealUrl() {
            log('使用真实视频URL测试VLC...');
            
            // 使用一个公开的测试视频URL
            const testUrl = 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4';
            
            if (window.LoraxEnhanced && window.LoraxEnhanced.callMacVLC) {
                window.LoraxEnhanced.callMacVLC(testUrl, '示例视频');
                log('真实URL VLC测试完成');
                showStatus('vlc-status', '真实URL VLC测试完成', 'success');
            } else {
                log('VLC功能不可用', 'error');
                showStatus('vlc-status', 'VLC功能不可用', 'error');
            }
        }

        // DPlayer测试函数
        function testDPlayerDebug() {
            log('开始测试DPlayer调试信息...');
            
            if (window.LoraxEnhanced && window.LoraxEnhanced.printDPlayerInfo) {
                const testM3u8 = [
                    {name: '超清', url: 'http://test.url/hd.m3u8', type: 'hls'},
                    {name: '高清', url: 'http://test.url/sd.m3u8', type: 'hls'},
                    {name: '标清', url: 'http://test.url/ld.m3u8', type: 'hls'}
                ];
                
                window.LoraxEnhanced.printDPlayerInfo(testM3u8, '测试视频');
                log('DPlayer调试信息已打印到控制台');
                showStatus('dplayer-status', 'DPlayer调试信息已打印到控制台，请查看Console', 'success');
            } else {
                log('DPlayer调试功能不可用', 'error');
                showStatus('dplayer-status', 'DPlayer调试功能不可用', 'error');
            }
        }

        function testM3U8Parsing() {
            log('测试M3U8解析功能...');
            
            // 模拟M3U8内容解析
            const mockM3u8Content = `#EXTM3U
#EXT-X-VERSION:3
#EXT-X-TARGETDURATION:10
#EXTINF:9.009,
segment1.ts
#EXTINF:9.009,
segment2.ts
#EXT-X-ENDLIST`;
            
            log('模拟M3U8内容解析完成');
            showStatus('dplayer-status', 'M3U8解析测试完成', 'success');
        }

        function clearConsole() {
            console.clear();
            log('控制台已清空');
        }

        // 模拟播放函数
        function playWithVLC(pickCode, fileName) {
            log(`使用VLC播放: ${fileName} (${pickCode})`);
            
            if (window.LoraxEnhanced && window.LoraxEnhanced.playVideo) {
                window.LoraxEnhanced.playVideo(pickCode, fileName, 'VLC');
                log(`VLC播放请求已发送: ${fileName}`);
            } else {
                log('VLC播放功能不可用', 'error');
            }
        }

        function playWithDPlayer(pickCode, fileName) {
            log(`使用DPlayer播放: ${fileName} (${pickCode})`);
            
            if (window.LoraxEnhanced && window.LoraxEnhanced.playVideo) {
                window.LoraxEnhanced.playVideo(pickCode, fileName, 'Dplayer');
                log(`DPlayer播放请求已发送: ${fileName}`);
            } else {
                log('DPlayer播放功能不可用', 'error');
            }
        }

        // 综合测试
        function runAllTests() {
            log('=== 开始运行所有测试 ===');
            
            setTimeout(() => testVLCProtocol(), 500);
            setTimeout(() => testDPlayerDebug(), 1000);
            setTimeout(() => testM3U8Parsing(), 1500);
            setTimeout(() => {
                log('=== 所有测试完成 ===');
                showStatus('vlc-status', '所有测试已完成，请查看日志', 'success');
            }, 2000);
        }

        function checkScriptStatus() {
            log('检查脚本状态...');
            
            if (window.LoraxEnhanced) {
                log('✅ LoraxEnhanced对象已加载');
                log(`✅ 配置信息: ${JSON.stringify(window.LoraxEnhanced.config)}`);
                
                const methods = ['playVideo', 'callMacVLC', 'printDPlayerInfo', 'testFeatures'];
                methods.forEach(method => {
                    if (window.LoraxEnhanced[method]) {
                        log(`✅ 方法 ${method} 可用`);
                    } else {
                        log(`❌ 方法 ${method} 不可用`);
                    }
                });
                
                showStatus('dplayer-status', '脚本状态检查完成，功能正常', 'success');
            } else {
                log('❌ LoraxEnhanced对象未找到');
                log('请确保用户脚本已正确安装并启用');
                showStatus('dplayer-status', '脚本未加载，请检查用户脚本', 'error');
            }
        }

        function showAPIInfo() {
            log('=== API信息 ===');
            
            if (window.LoraxEnhanced) {
                log('可用的API方法:');
                log('- LoraxEnhanced.playVideo(pickCode, fileName, playerType)');
                log('- LoraxEnhanced.callMacVLC(videoUrl, videoName)');
                log('- LoraxEnhanced.printDPlayerInfo(m3u8List, videoName)');
                log('- LoraxEnhanced.testFeatures()');
                log('- LoraxEnhanced.config (配置对象)');
                
                showStatus('vlc-status', 'API信息已显示在日志中', 'info');
            } else {
                log('API不可用，脚本未加载');
                showStatus('vlc-status', 'API不可用', 'error');
            }
        }

        // 页面加载完成后的初始化
        window.addEventListener('load', function() {
            log('测试页面加载完成');
            
            // 检查脚本是否加载
            setTimeout(() => {
                if (window.LoraxEnhanced) {
                    log('✅ 115Lorax增强功能脚本已加载');
                    
                    // 运行脚本自带的测试
                    if (window.LoraxEnhanced.testFeatures) {
                        window.LoraxEnhanced.testFeatures();
                    }
                } else {
                    log('⚠️ 115Lorax增强功能脚本未检测到');
                    log('请确保已安装用户脚本并刷新页面');
                }
            }, 1000);
        });
    </script>
</body>
</html>
